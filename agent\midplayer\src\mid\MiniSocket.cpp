﻿#include "stdafx.h"
#include "MiniSocket.h"
#pragma comment( lib, "ws2_32.lib")
#pragma comment( lib, "wsock32.lib" )

CMiniSocket::CMiniSocket()
{
	socketTimeout = 30; // 超时时间，秒为单位
	errorMessage = nullptr;
	isConnected = false;
}

CMiniSocket::~CMiniSocket()
{
	closeSocket();
	if (errorMessage != nullptr) {
		delete[] errorMessage;
		errorMessage = nullptr;
	}
}

int CMiniSocket::initWinSocket()
{
	WSADATA wsaData;
	if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
		return -1;
	}

	if (LOBYTE(wsaData.wVersion) != 2 || HIBYTE(wsaData.wVersion) != 2) {
		WSACleanup();
		return -1;
	}

	return 0;
}

int CMiniSocket::connectToServer(const char* address, int port, int timeout) {
	sockaddr_in tcpServerAddr;
	socketHandle = socket(AF_INET, SOCK_STREAM, 0);
	if (socketHandle == INVALID_SOCKET) {
		logErrorMessage("创建Socket失败!");
		return -1;
	}

	socketTimeout = timeout;
	setNonBlockingMode(true);

	tcpServerAddr.sin_family = AF_INET;
	tcpServerAddr.sin_addr.s_addr = inet_addr(address);
	tcpServerAddr.sin_port = htons(port);
	if (connect(socketHandle, (SOCKADDR*)&tcpServerAddr, sizeof(tcpServerAddr)) == SOCKET_ERROR) {
		int lastError = WSAGetLastError();
		if (lastError == WSAEWOULDBLOCK) {
			if (waitReply(timeout) != 0) {
				logErrorMessage("connectToServer超时");
				closeSocket(); // 确保资源释放
				return -1;
			}
		}
		else {
			logErrorMessage("connectToServer错误");
			closeSocket(); // 确保资源释放
			return -2;
		}
	}

	setNonBlockingMode(false);
	setReadTimeout(socketTimeout * 1000);
	setWriteTimeout(socketTimeout * 1000);

	strncpy(serverAddress, address, 31);
	serverAddress[31] = '\0'; // 确保字符串以 \0 结尾
	serverPort = port;
	isConnected = true;
	return 0;
}

int CMiniSocket::reconnect(int timeout) {
	closeSocket();
	return connectToServer(serverAddress, serverPort, timeout);
}

int CMiniSocket::closeSocket() {
	if (socketHandle != INVALID_SOCKET) {
		closesocket(socketHandle);
		socketHandle = INVALID_SOCKET;
	}
	isConnected = false;
	return 0;
}

int CMiniSocket::clearBuffer(int waitTime) {
	int length = receiveLength(waitTime);
	if (length <= 0) {
		return length;
	}
	char* buffer = new char[length];
	if (buffer) {
		receiveBuffer(buffer, length);
		delete[] buffer;
	}
	return 0;
}

int CMiniSocket::receiveLength(int waitTime) {
	u_long len = 0;
	fd_set fdrEvent;
	struct timeval timeout = { waitTime, 0 };

	FD_ZERO(&fdrEvent);
	FD_SET(socketHandle, &fdrEvent);

	if (select(0, &fdrEvent, nullptr, nullptr, &timeout) <= 0)
		return 0;

	if (ioctlsocket(socketHandle, FIONREAD, &len) == SOCKET_ERROR) {
		setErrorMessage("MiniSocket::receiveLength::ioctlsocket失败");
		return 0;
	}
	return len;
}

//等待接收,如果返回<0则表示无数据 =0表示超时 >0表示有数据可接收
int CMiniSocket::waitReceive(int waitTime) {
	time_t startTime = time(nullptr);
	int retCode = -1;
	while (time(nullptr) - startTime < waitTime) {
		if ((retCode = receiveLength()) > 0) {
			break;
		}
		Sleep(1);
	}
	return retCode;
}

//--------------------------------------------------------------
/*
int CMiniSocket::ReceiveBuf(void *p_pSendBuf,int p_iBuffLen)
作用:读取一定数量的数据，读取足够数量或出错或超时的时候返回
返回:-1:类Socket无效
	 -2:WinSocket出现严重错误,类Socket被自动关闭
	 -3:send时出错,不重试
	 -99:等待发送数据超时
	 其它:接收数据包的大小
作者:罗时俊 2003/10/24
*/
int CMiniSocket::receiveBuffer(void* buffer, int bufferSize) {
	if (socketHandle == INVALID_SOCKET) {
		logErrorMessage("MiniSocket::receiveBuffer发现无效的Socket");
		return -1;
	}

	int retCode = -1;
	long bytesRead = 0;
	time_t startTime = time(nullptr);

	memset(buffer, 0, bufferSize);
	auto recvStartTime = std::chrono::high_resolution_clock::now();

	while ((time(nullptr) - startTime) < socketTimeout) {
		if (waitReceive(socketTimeout) > 0) {
			retCode = recv(socketHandle, (char*)buffer + bytesRead, bufferSize - bytesRead, 0);
			auto recvEndTime = std::chrono::high_resolution_clock::now();
			if (retCode == SOCKET_ERROR) {
				// 计算recv耗时
				std::chrono::duration<double, std::milli> recvDuration = recvEndTime - recvStartTime;
				std::stringstream errorStream;
				errorStream << "[receiveBuffer::recv]耗时: " << std::to_string(recvDuration.count()) << " 毫秒";
				logErrorMessage(errorStream.str().c_str());
				return -3;
			}
			bytesRead += retCode;
			if (bufferSize - bytesRead <= 0) {
				return bufferSize;
			}
		}
	}

	// 计算recv耗时
	auto recvEndTime = std::chrono::high_resolution_clock::now();
	std::chrono::duration<double, std::milli> recvDuration = recvEndTime - recvStartTime;
	std::stringstream errorStream;
	errorStream << "receiveBuffer::recv超时: " << std::to_string(recvDuration.count()) << " 毫秒";
	logErrorMessage(errorStream.str().c_str());

	return -99;
}

/*
int CMiniSocket::sendBuffer(const void* buffer, int bufferSize)
作用:往此类Socket上发送数据p_pSendBuff,
返回:-1:类Socket无效
	 -2:WinSocket出现严重错误,类Socket被自动关闭
	 -3:send时出错,不重试
	 -99:等待发送数据超时
	 其它:发送数据的大小
作者:罗时俊 2003/10/24
*/
int CMiniSocket::sendBuffer(const void* buffer, int bufferSize) {
	fd_set fdwEvent;
	struct timeval timeout;
	time_t startTime = time(nullptr);

	// 已处理的数据长度
	int callResult = 0;
	int totalSent = 0;
	// 最大send重试次数
	const int maxSendRetries = 1;

	if (socketHandle == INVALID_SOCKET) {
		logErrorMessage("sendBuffer发现无效的Socket");
		return -1;
	}

	callResult = 0;
	int sendRetryCount = 0;
	while ((time(nullptr) - startTime) < socketTimeout) {
		do {
			FD_ZERO(&fdwEvent);
			FD_SET(socketHandle, &fdwEvent);
			timeout.tv_sec = socketTimeout;
			timeout.tv_usec = 0;
			callResult = select(socketHandle, (fd_set*)0, &fdwEvent, (fd_set*)0, &timeout);
		} while ((callResult == SOCKET_ERROR) && (time(nullptr) - startTime < socketTimeout));

		if (callResult == SOCKET_ERROR) {
			logErrorMessage("sendBuffer::select失败");
			return -2;
		}
		else if (callResult == 0) {
			// select 返回0表示在指定的超时时间内没有fd准备好进行写操作
			Sleep(10);
		}
		else if (callResult > 0) {
			// select 返回正值，表示 socketHandle 已经准备好进行写操作
			while (totalSent < bufferSize) {
				auto sendStartTime = std::chrono::high_resolution_clock::now();
				callResult = send(socketHandle, (const char*)buffer + totalSent, bufferSize - totalSent, MSG_DONTROUTE);
				auto sendEndTime = std::chrono::high_resolution_clock::now();

				if (callResult == SOCKET_ERROR) {
					// 计算send耗时
					std::stringstream errorStream;
					std::chrono::duration<double, std::milli> sendDuration = sendEndTime - sendStartTime;
					errorStream << "[MiniSocket::sendBuffer::send]耗时: " << std::to_string(sendDuration.count()) << " 毫秒";
					logErrorMessage(errorStream.str().c_str());

					if (++sendRetryCount >= maxSendRetries)
						return -3;
					Sleep(10); // 等待一段时间后重试
					continue;
				}
				else if (callResult == 0) {
					// 连接已关闭
					logErrorMessage("sendBuffer::连接已关闭");
					return -4;
				}
				totalSent += callResult;
				sendRetryCount = 0;
				// 重置重试计数器等待发送
				if (totalSent < bufferSize) {
					FD_ZERO(&fdwEvent);
					FD_SET(socketHandle, &fdwEvent);
					select(socketHandle, (fd_set*)0, &fdwEvent, (fd_set*)0, &timeout);
				}
			}
			return totalSent;
		}
		else {
			// select 返回负值，表示发生了错误
			logErrorMessage("sendBuffer::select错误");
			return -99;
		}
	}

	setErrorMessage("MiniSocket::sendBuffer超时");
	return -99;
}

void CMiniSocket::logErrorMessage(const char* errinfo)
{
	if (!errinfo || !*errinfo) return ;

	LPSTR buffer = nullptr;
	DWORD dwCode = ::GetLastError();
	auto status = ::FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
		nullptr, dwCode, 0, (LPSTR)&buffer, 0, nullptr);

	if (status != 0 && buffer != nullptr)
	{
		setErrorMessage(buffer);
		LOG4CXX_TRACE(logger, errinfo << "：" << buffer);
		LocalFree(buffer);
	}
	else
	{
		LOG4CXX_TRACE(logger, "Failed to retrieve error message.");
	}
}

unsigned char CMiniSocket::binaryToHex(char ch) {
	if (ch >= '0' && ch <= '9')
		return (ch - '0');
	if (ch >= 'A' && ch <= 'F')
		return (ch - 'A' + 10);
	if (ch >= 'a' && ch <= 'f')
		return (ch - 'a' + 10);
	return 0;
}

void CMiniSocket::asciiToHex(char* dest, const char* source, int count) {
	while (count--) {
		*dest++ = (binaryToHex(*source++) << 4) + binaryToHex(*source++);
	}
}

const char* CMiniSocket::getErrorMessage() const {
	return errorMessage;
}

void CMiniSocket::setErrorMessage(const char* errorMessage) {
	size_t length = strlen(errorMessage) + 1;
	if (this->errorMessage != nullptr)
		delete[] this->errorMessage;

	this->errorMessage = new char[length];
	strncpy(this->errorMessage, errorMessage, length);
}

int CMiniSocket::setReadTimeout(int timeout) {
	if (setsockopt(socketHandle, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout)) == SOCKET_ERROR)
		return -1;
	return 0;
}

int CMiniSocket::setWriteTimeout(int timeout) {
	if (setsockopt(socketHandle, SOL_SOCKET, SO_SNDTIMEO, (const char*)&timeout, sizeof(timeout)) == SOCKET_ERROR)
		return -1;
	return 0;
}

int CMiniSocket::setNonBlockingMode(bool mode) {
	u_long nonBlocking = mode ? 1 : 0;
	if (ioctlsocket(socketHandle, FIONBIO, &nonBlocking) == SOCKET_ERROR)
		return -1;
	return 0;
}

//select 模型，即设置超时
int CMiniSocket::waitReply(int timeout)
{
	fd_set wEvent;
	struct timeval tv;

	FD_ZERO(&wEvent);
	FD_SET(socketHandle, &wEvent);
	tv.tv_sec = timeout;
	tv.tv_usec = 0;

	// 对于select函数来说，连接成功时会触发写事
	int result = ::select(0, nullptr, &wEvent, nullptr, &tv);
	if (result == SOCKET_ERROR) {
		logErrorMessage("waitReply::select错误");
		return -1;
	}
	else if (result == 0) {
		logErrorMessage("waitReply::select超时");
		return -2;
	}

	return 0;
}

SOCKET CMiniSocket::getSocket() const
{
	return socketHandle;
}