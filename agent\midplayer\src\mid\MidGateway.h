﻿/***************************************************************************
  MidGateway.cpp : implementation file
  金证新一代柜台外围网关通讯类
  author: luosj 200610
****************************************************************************/
#ifndef _KD_GATEWAY_H__
#define _KD_GATEWAY_H__
#pragma once
#include "MiniSocket.h"
#include "error.h"
#include <vector>
#include <string>
#include <functional>

#define DEF_SBUFLEN 32    //定义小缓冲区大小
#define DEF_LBUFLEN_1024 1024  //定义大缓冲区大小
#define DEF_LBUFLEN_8192 8192  //定义大缓冲区大小

struct tagAnswerPacket //应答包
{
	char szCRCCode[DEF_SBUFLEN];  //3CRC校验码	CHAR(8), 见注5,7
	char szVersion[DEF_SBUFLEN];  //4版本号	当前协议版本编号(固定串“KDGATEWAY1.0”)
	char szRetCode[DEF_SBUFLEN];  //5返回码	VARCHAR(10), “0”表示正常
	char szRetMsg[DEF_LBUFLEN_8192];   //6返回信息	VARCHAR(200),返回非0则表示交易处理出现某种交易错误或系统错误(见数据字典)
	char szReqFunNop[DEF_SBUFLEN]; //11原请求功能号	VARCHAR(20)       2006-4-12增加
	char szPacketID[DEF_SBUFLEN];      //11请求包序列号	VARCHAR(20)       2006-4-12增加
	char szReserved[DEF_SBUFLEN]; //保留字段3	VARCHAR(20)

	int bIsNext;    //后续包标示	CHAR(1),‘0’－无，‘1’－表示有后续包 (取后续包发99请求)
	int iFieldNum;  //应答字段数	VARCHAR(10)
	int iRecNum;    //应答记录数	VARCHAR(10)
	char* pBuffer;  //指到接收的数据缓冲区

	int iHeadLen;  //包头长度
	DWORD dwDataLen;  //数据长度
};

// 定义数据结构存储应答包头的各个字段
struct MidRespRecord
{
	std::string headerLength;	// 报头长度
	std::string dataLength;		// 数据长度
	uint32_t respCode = -1;	// 应答码

	std::string crcValue;			// 校验和
	std::string protoVersion;	// 协议版本
	std::string respMessage;	// 应答消息
	std::string hasNextPacket;// 是否有下一个包
	std::string fieldCount;		// 字段数量
	std::string recordCount;	// 记录数量
	std::string origFuncCode;	// 原始请求功能码
	std::string reqPacketSeq;	// 请求包序列号
	std::vector<std::string> reservedField;	// 保留字段
};

/////////////////////////////////////////////////////////////////////////////
// MidGateway 金证新一代网关接口
class MidGateway :public CError
{
public:
	MidGateway();
	~MidGateway();

	void setTrace(bool btrace);

	bool connectToMidStdGW();

	bool connectToMidTdxGW();

	//与KDGateway建立连接
	bool connectToServer(const char* serverAddress, WORD serverPort, int timeOut = 10);

	//与KDGateway断开连接
	bool disconnect();

	//与KDGateway重新连接
	bool reConnect();

	//发送请求包
	bool sendMessage(const char* message);

	//发送请求包
	bool sendRequestOpt(std::vector<std::string> vecData);

	//接收请答包，crcCode表示是否要对收包进行CRC校验，因为签入包是不需要校验的
	bool recvtagMessage(tagAnswerPacket* answerPacket, int crcCode = 0);

	bool receiveMessage(MidRespRecord& record, uint64_t num = 0);

	bool receiveMessage(std::string& reqsession, MidRespRecord& record, uint64_t num = 0);

	//签入KDGateway
	bool signSystem();

	// 检查系统
	bool readySystem();

	//登录
	bool loginSystem(char userType, char* userID, char* szPasswd);

	//发送请求
	bool waitAnswer(const char* szRequest);

	//请求应答
	bool waitQuery(std::string& request, std::string& user, std::string& stk, uint64_t num);

	//请求应答
	bool waitMatch(std::string& request, std::string& user, std::string& stk, uint64_t num);

	//请求应答
	bool waitAnswer(std::string& request, std::string& anser, uint64_t num);

	//发送请求
	bool waitAnswerOpt(std::vector<std::string> vecData, std::function<void()> sync_func = nullptr);

	//*****************  处理接收到的包 *****************
	  //为了程序简单，不设置地址入参，注意缓冲区长度
	  //设置包头常量
	inline void setParams(const char* param);
	inline void setOpUser(const char* opUser) { strncpy(m_opUser, opUser, DEF_SBUFLEN); };   //操作员代码
	inline void setOpRole(const char* opRole) { strncpy(m_opRole, opRole, DEF_SBUFLEN); };   //操作角色
	inline void setOpSite(const char* opSite) { strncpy(m_opSite, opSite, DEF_SBUFLEN); };   //操作站点
	inline void setOpBranch(const char* opBranch) { strncpy(m_opBranch, opBranch, DEF_SBUFLEN); };   //操作分支
	inline void setOpChannel(const char* opChannel) { strncpy(m_opChannel, opChannel, DEF_SBUFLEN); };   //操作渠道
	inline void setOpRouter(const char* opRouter) { strncpy(m_opRouter, opRouter, DEF_SBUFLEN); };

	//得到包头常量
	inline char* getOpUser() { return m_opUser; };   //操作员代码
	inline char* getOpRole() { return m_opRole; };   //操作角色
	inline char* getOpSite() { return m_opSite; };   //操作站点
	inline char* getOpBranch() { return m_opBranch; };   //操作分支
	inline char* getOpChannel() { return m_opChannel; };   //操作渠道
	inline char* getOpRouter() { return m_opRouter; };

	inline char* getRequestBuffer() { return m_szRequestBuffer; }; //得到发送缓冲区
	inline char* getWorkKey() { return m_workKey; }; //得到发送缓冲区

	// 解析包
	const char* getDataHead();
	bool getDataRows(std::vector<std::string>& data_rows);
	bool getDataRowsOpt(std::vector<std::string>& data_rows);

	bool extract(const char* data, int field, size_t& value);
	bool extract(const char* data, int field, std::string& value);
	bool rebuildMidFields(std::string& message);
	bool rewriteMidFields(std::string& message);
	bool refactorAllFields(std::string& message);
	bool rebuildMatchQuery(std::string& request, std::string& user, std::string& stk);
	bool getRow(const char* src, char* dest, int filed_num, int row_num, int data_len, char ch);

private:

	bool parseResponse(const char* str, int len, MidRespRecord& record);
	void logErrorMessage(int errcode, const char* errInfo, ...);
	void logErrorMessage(const char* errInfo, int errCode = -1, bool sysError = false);

	// 字符串分割函数
	std::vector<std::string> splitStream(const std::string& str, char delimiter);
	std::vector<std::string> splitStream(const char* str, int len, char delimiter);
	std::vector<std::string> splitString(const std::string& str, const std::string& pattern);

public:
	//  CKDGWResult
	char* m_returnDataBuffer;       //返回的数据区，初始分配m_iDataBufferSize=512K
	DWORD m_dwDataBufferSize;

	DWORD m_dwTotalDataLen;    //缓冲区内总的数据长度
	DWORD m_dwRecordNo;           //记录总数

	char* m_singleRecvBuffer;   //单包缓冲区
	int m_singleRecvBufferSize; //单包缓冲区大小
	char m_szRequestBuffer[DEF_LBUFLEN_8192]; //请求缓冲区
	char m_responDataBuffer[DEF_LBUFLEN_8192];
	char m_szReturnHead[DEF_LBUFLEN_1024];

	static std::mutex g_dataMutex; // 静态互斥锁
	static std::map<uint64_t, std::string> g_anserData;

private:

	//请求相关信息
	char m_opUser[DEF_SBUFLEN];   //操作员代码
	char m_opRole[DEF_SBUFLEN];   //操作角色
	char m_opSite[DEF_SBUFLEN];   //操作站点
	char m_opBranch[DEF_SBUFLEN]; //操作分支
	char m_opChannel[DEF_SBUFLEN];   //操作渠道

	DWORD m_dwReqPakSN;              //包头序列号，防止串户

	char m_szVersion[DEF_SBUFLEN];   //接口版本号:"KDGATEWAY1.2"
	char m_crcCode[DEF_SBUFLEN];   //CRC校验
	char m_workKey[DEF_SBUFLEN];   //工作密钥
	char m_opSession[DEF_SBUFLEN];   // 通讯Session
	char m_opRouter[DEF_SBUFLEN];    // 通讯Session

	bool m_btraceon = false;
	bool m_signsystem = false;
	CMiniSocket* m_pSocket;
};

#endif
