﻿#include "stdafx.h"
#include "AxConfig.h"
#include <fstream>
#include <sstream>
#include <thread>

MidPlayerConfig& MidPlayerConfig::getInstance() {
	static MidPlayerConfig instance;
	return instance;
}

MidPlayerResult MidPlayerConfig::loadFromFile(const std::string& filename) {
	std::lock_guard<std::mutex> lock(configMutex_);

	configFilename_ = filename;
	return parseConfigFile(filename);
}

MidPlayerResult MidPlayerConfig::parseConfigFile(const std::string& filename) {
	std::ifstream file(filename);
	if (!file.is_open()) {
		return MIDPLAYER_ERROR_MSG(MidPlayerError::CONFIG_FILE_NOT_FOUND,
			"无法打开配置文件: " + filename);
	}

	configData_.clear();
	std::string line;
	std::string currentSection;
	int lineNumber = 0;

	while (std::getline(file, line)) {
		lineNumber++;

		// 去除行首和行尾的空格
		line.erase(0, line.find_first_not_of(" \t"));
		line.erase(line.find_last_not_of(" \t") + 1);

		// 跳过空行和注释行
		if (line.empty() || line[0] == ';' || line[0] == '#') {
			continue;
		}

		// 处理段标题
		if (line[0] == '[' && line.back() == ']') {
			currentSection = line.substr(1, line.length() - 2);
			continue;
		}

		// 处理键值对
		size_t delimiterPos = line.find('=');
		if (delimiterPos == std::string::npos) {
			return MIDPLAYER_ERROR_MSG(MidPlayerError::CONFIG_PARSE_ERROR,
				"配置文件第" + std::to_string(lineNumber) + "行格式错误: " + line);
		}

		std::string key = line.substr(0, delimiterPos);
		std::string value = line.substr(delimiterPos + 1);

		// 去除键和值的空格
		key.erase(key.find_last_not_of(" \t") + 1);
		value.erase(0, value.find_first_not_of(" \t"));

		if (currentSection.empty()) {
			return MIDPLAYER_ERROR_MSG(MidPlayerError::CONFIG_PARSE_ERROR,
				"配置文件第" + std::to_string(lineNumber) + "行缺少段标题");
		}

		configData_[currentSection][key] = value;
	}

	return validateConfig();
}

MidPlayerResult MidPlayerConfig::saveToFile(const std::string& filename) const {
	std::lock_guard<std::mutex> lock(configMutex_);

	std::ofstream file(filename);
	if (!file.is_open()) {
		return MIDPLAYER_ERROR_MSG(MidPlayerError::FILE_WRITE_FAILED,
			"无法写入配置文件: " + filename);
	}

	for (const auto& section : configData_) {
		file << "[" << section.first << "]\n";
		for (const auto& keyValue : section.second) {
			file << keyValue.first << "=" << keyValue.second << "\n";
		}
		file << "\n";
	}

	return MIDPLAYER_SUCCESS();
}

MidPlayerResult MidPlayerConfig::reloadConfig() {
	if (configFilename_.empty()) {
		return MIDPLAYER_ERROR(MidPlayerError::CONFIG_FILE_NOT_FOUND);
	}
	return loadFromFile(configFilename_);
}

std::string MidPlayerConfig::getString(ConfigSection section, const std::string& key, const std::string& defaultValue) const {
	return getValue<std::string>(section, key, defaultValue);
}

int MidPlayerConfig::getInt(ConfigSection section, const std::string& key, int defaultValue) const {
	return getValue<int>(section, key, defaultValue);
}

bool MidPlayerConfig::getBool(ConfigSection section, const std::string& key, bool defaultValue) const {
	return getValue<bool>(section, key, defaultValue);
}

double MidPlayerConfig::getDouble(ConfigSection section, const std::string& key, double defaultValue) const {
	return getValue<double>(section, key, defaultValue);
}

std::string MidPlayerConfig::sectionToString(ConfigSection section) const {
	switch (section) {
	case ConfigSection::MAIN: return "main";
	case ConfigSection::MID_CFG: return "mid_cfg";
	case ConfigSection::KDG_STD: return "kdg_std";
	case ConfigSection::KDG_TDX: return "kdg_tdx";
	case ConfigSection::LOGGING: return "logging";
	case ConfigSection::PERFORMANCE: return "performance";
	case ConfigSection::NETWORK: return "network";
	default: return "unknown";
	}
}

ConfigSection MidPlayerConfig::stringToSection(const std::string& sectionName) const {
	if (sectionName == "main") return ConfigSection::MAIN;
	if (sectionName == "mid_cfg") return ConfigSection::MID_CFG;
	if (sectionName == "kdg_std") return ConfigSection::KDG_STD;
	if (sectionName == "kdg_tdx") return ConfigSection::KDG_TDX;
	if (sectionName == "logging") return ConfigSection::LOGGING;
	if (sectionName == "performance") return ConfigSection::PERFORMANCE;
	if (sectionName == "network") return ConfigSection::NETWORK;
	return ConfigSection::MAIN;
}

MidPlayerConfig::MainConfig MidPlayerConfig::getMainConfig() const {
	MainConfig config;
	config.readOnly = getBool(ConfigSection::MAIN, "read_only", false);
	config.stgEnable = getBool(ConfigSection::MAIN, "stg_enable", false);
	config.utilMatch = getBool(ConfigSection::MAIN, "util_match", false);
	config.beginMid = getString(ConfigSection::MAIN, "begin_mid", "ipdst");
	config.memchrAlgo = getString(ConfigSection::MAIN, "memchr_algo", "find");
	config.debugLevel = getInt(ConfigSection::MAIN, "debug_level", 1);
	config.batchNum = getInt(ConfigSection::MAIN, "batch_num", 51200);
	config.threadNum = getInt(ConfigSection::MAIN, "thread_num", 0);
	config.retryCount = getInt(ConfigSection::MAIN, "retry_count", 8);
	config.userField = getInt(ConfigSection::MAIN, "user_filed", 5);
	config.funcField = getInt(ConfigSection::MAIN, "func_filed", 13);
	config.stkcField = getInt(ConfigSection::MAIN, "stkc_filed", 19);
	config.typeField = getInt(ConfigSection::MAIN, "type_filed", 20);
	config.codeField = getInt(ConfigSection::MAIN, "code_filed", 5);

	// 自动检测线程数
	if (config.threadNum == 0) {
		config.threadNum = std::thread::hardware_concurrency();
		if (config.threadNum == 0) config.threadNum = 4;  // 默认4个线程
	}

	return config;
}

MidPlayerConfig::MidCfgConfig MidPlayerConfig::getMidCfgConfig() const {
	MidCfgConfig config;
	config.reqFlag = getString(ConfigSection::MID_CFG, "req_flag", "REQ:");
	config.ansFlag = getString(ConfigSection::MID_CFG, "ans_flag", "RESP:");
	config.skipFunc = getString(ConfigSection::MID_CFG, "skip_func", "90,100,301,368,1760");
	config.skipMarket = getString(ConfigSection::MID_CFG, "skip_market", "20,30");
	return config;
}

MidPlayerConfig::GatewayConfig MidPlayerConfig::getStdGatewayConfig() const {
	GatewayConfig config;
	config.server = getString(ConfigSection::KDG_STD, "server", "127.0.0.1");
	config.port = getInt(ConfigSection::KDG_STD, "port", 9100);
	config.timeout = getInt(ConfigSection::KDG_STD, "timeout", 45);
	config.checkin = getString(ConfigSection::KDG_STD, "checkin", "0000|0000|CRCCRCCR|KDGATEWAY1.2|9988|127.0.0.1|999|4|SS|2|");
	config.version = getString(ConfigSection::KDG_STD, "version", "KDGATEWAY1.2");
	config.crcCode = getString(ConfigSection::KDG_STD, "crc_code", "12345678");
	config.workKey = getString(ConfigSection::KDG_STD, "work_key", "12345678");
	return config;
}

MidPlayerConfig::GatewayConfig MidPlayerConfig::getTdxGatewayConfig() const {
	GatewayConfig config;
	config.server = getString(ConfigSection::KDG_TDX, "server", "127.0.0.1");
	config.port = getInt(ConfigSection::KDG_TDX, "port", 9200);
	config.timeout = getInt(ConfigSection::KDG_TDX, "timeout", 45);
	config.checkin = getString(ConfigSection::KDG_TDX, "checkin", "0000|0000|CRCCRCCR|KDGATEWAY1.2|9988|127.0.0.1|999|4|SS|2|");
	config.version = getString(ConfigSection::KDG_TDX, "version", "KDGATEWAY1.2");
	config.crcCode = getString(ConfigSection::KDG_TDX, "crc_code", "12345678");
	config.workKey = getString(ConfigSection::KDG_TDX, "work_key", "12345678");
	return config;
}

MidPlayerConfig::LoggingConfig MidPlayerConfig::getLoggingConfig() const {
	LoggingConfig config;
	config.logLevel = getString(ConfigSection::LOGGING, "log_level", "INFO");
	config.logFile = getString(ConfigSection::LOGGING, "log_file", "logs/midplayer.log");
	config.rotationType = getString(ConfigSection::LOGGING, "rotation_type", "daily");
	config.rotationHour = getInt(ConfigSection::LOGGING, "rotation_hour", 0);
	config.rotationMinute = getInt(ConfigSection::LOGGING, "rotation_minute", 0);
	config.maxFileSize = getInt(ConfigSection::LOGGING, "max_file_size", 10485760);
	config.maxFiles = getInt(ConfigSection::LOGGING, "max_files", 30);
	config.consoleOutput = getBool(ConfigSection::LOGGING, "console_output", true);
	config.fileOutput = getBool(ConfigSection::LOGGING, "file_output", true);
	config.singleThread = getBool(ConfigSection::LOGGING, "single_thread", true);
	config.consolePattern = getString(ConfigSection::LOGGING, "console_pattern", "%Y-%m-%d %H:%M:%S.%e [%t] [%^%l%$] %n - %v");
	config.filePattern = getString(ConfigSection::LOGGING, "file_pattern", "%Y-%m-%d %H:%M:%S.%e [%t] [%l] %n - %v");
	return config;
}

MidPlayerConfig::PerformanceConfig MidPlayerConfig::getPerformanceConfig() const {
	PerformanceConfig config;
	config.mmapBufferSize = getInt(ConfigSection::PERFORMANCE, "mmap_buffer_size", 67108864);
	config.sendBufferSize = getInt(ConfigSection::PERFORMANCE, "send_buffer_size", 65536);
	config.recvBufferSize = getInt(ConfigSection::PERFORMANCE, "recv_buffer_size", 65536);
	config.messageBatchSize = getInt(ConfigSection::PERFORMANCE, "message_batch_size", 1000);
	config.statsUpdateInterval = getInt(ConfigSection::PERFORMANCE, "stats_update_interval", 1000);
	config.progressUpdateInterval = getInt(ConfigSection::PERFORMANCE, "progress_update_interval", 500);
	return config;
}

MidPlayerConfig::NetworkConfig MidPlayerConfig::getNetworkConfig() const {
	NetworkConfig config;
	config.connectTimeout = getInt(ConfigSection::NETWORK, "connect_timeout", 30);
	config.sendTimeout = getInt(ConfigSection::NETWORK, "send_timeout", 10);
	config.recvTimeout = getInt(ConfigSection::NETWORK, "recv_timeout", 10);
	config.heartbeatInterval = getInt(ConfigSection::NETWORK, "heartbeat_interval", 30);
	config.reconnectInterval = getInt(ConfigSection::NETWORK, "reconnect_interval", 5);
	config.maxReconnectAttempts = getInt(ConfigSection::NETWORK, "max_reconnect_attempts", 3);
	return config;
}

MidPlayerResult MidPlayerConfig::validateConfig() const {
	// 验证主配置
	auto mainConfig = getMainConfig();
	if (mainConfig.batchNum <= 0) {
		return MIDPLAYER_ERROR_MSG(MidPlayerError::CONFIG_INVALID_VALUE, "batch_num必须大于0");
	}
	if (mainConfig.threadNum <= 0) {
		return MIDPLAYER_ERROR_MSG(MidPlayerError::CONFIG_INVALID_VALUE, "thread_num必须大于0");
	}

	// 验证网关配置
	auto stdConfig = getStdGatewayConfig();
	if (stdConfig.port <= 0 || stdConfig.port > 65535) {
		return MIDPLAYER_ERROR_MSG(MidPlayerError::CONFIG_INVALID_VALUE, "kdg_std端口号无效");
	}

	auto tdxConfig = getTdxGatewayConfig();
	if (tdxConfig.port <= 0 || tdxConfig.port > 65535) {
		return MIDPLAYER_ERROR_MSG(MidPlayerError::CONFIG_INVALID_VALUE, "kdg_tdx端口号无效");
	}

	return MIDPLAYER_SUCCESS();
}

void MidPlayerConfig::addConfigChangeListener(ConfigChangeCallback callback) {
	std::lock_guard<std::mutex> lock(configMutex_);
	changeListeners_.push_back(callback);
}

void MidPlayerConfig::notifyConfigChange(ConfigSection section, const std::string& key, const std::string& value) {
	for (const auto& listener : changeListeners_) {
		listener(section, key, value);
	}
}
