﻿#include "stdafx.h"

void MidMessage::SetRequestHeader(const tagMidRequst& request)
{
	m_request = request;
}

void MidMessage::SetRequestData(const std::string& requestData)
{
	m_request.requestData = requestData;
}

std::string MidMessage::GetRequestMessage()
{
	return serialize(m_request);
}

void MidMessage::SetAnswerHeader(const tagMidAnswer& answer)
{
	m_answer = answer;
}

std::string MidMessage::GetAnswerMessage()
{
	return serialize(m_answer);
}

tagMidRequst MidMessage::GetRequestHeader()
{
	return m_request;
}

tagMidAnswer MidMessage::GetAnswerHeader()
{
	return m_answer;
}

void MidMessage::SetAnswerData(const std::vector<std::pair<std::string, std::string>>& responseData)
{
	m_answer.responseData = responseData;
}

bool MidMessage::ParseMidMessage(uint64_t lineNum, const std::string& midMessage, tagMidRequst& midRequest, tagMidAnswer& midAnswer)
{
	std::string reqMsg, respMsg;

	size_t reqPos = midMessage.find("REQ:");
	size_t respPos = midMessage.find(",RESP:");

	if (reqPos != std::string::npos) {
		if (respPos != std::string::npos) {
			reqMsg = midMessage.substr(reqPos + 4, respPos - reqPos - 4);
			respMsg = midMessage.substr(respPos + 6);
		}
		else {
			reqMsg = midMessage.substr(reqPos + 4);
		}
	}

	ParseRequestMessage(lineNum, reqMsg, midRequest);

	ParseAnswerMessage(lineNum, respMsg, midAnswer);

	return true;
}

bool MidMessage::ParseRequestMessage(uint64_t lineNum, const std::string& requestMessage, tagMidRequst& midRequest)
{
	if (requestMessage.empty())
		return true;

	std::stringstream ss(requestMessage);
	std::string field;

	try {
		// 头长度
		std::getline(ss, field, '|');
		midRequest.m_headerLength = std::stoi(field);
		// 数据域长度
		std::getline(ss, field, '|');
		midRequest.m_dataLength = std::stoi(field);
		// CRC校验码
		std::getline(ss, midRequest.m_crcCheckCode, '|');
		// 版本号
		std::getline(ss, midRequest.m_versionNumber, '|');
		// 用户代码
		std::getline(ss, midRequest.m_userCode, '|');
		// 操作站点
		std::getline(ss, midRequest.m_operationSite, '|');
		// 开户分支
		std::getline(ss, midRequest.m_accountBranch, '|');
		// 操作渠道
		std::getline(ss, midRequest.m_operationChannel, '|');
		// 会话序号
		std::getline(ss, midRequest.m_sessionNumber, '|');
		// 用户角色
		std::getline(ss, midRequest.m_userRole, '|');
		// 请求包序列号
		std::getline(ss, midRequest.m_requestSerialN, '|');

		// 多柜台转发路由标记
		std::getline(ss, midRequest.m_routeFlag, '|');
		// 请求功能号
		std::getline(ss, midRequest.m_reqFunctionCode, '|');
		// 请求数据域
		std::getline(ss, midRequest.requestData, '|');
	}
	catch (const std::invalid_argument& ia) {
		LOG4CXX_ERROR(logger, "An exception occurred during request Analyzing:" << ia.what() << " on line:[" << lineNum << "]" << requestMessage);
		return false;
	}
	catch (const std::out_of_range& oe) {
		LOG4CXX_ERROR(logger, "An exception occurred during request Analyzing:" << oe.what() << " on line:[" << lineNum << "]" << requestMessage);
		return false;
	}
	catch (const std::exception& ex) {
		LOG4CXX_ERROR(logger, "An exception occurred during request Analyzing:" << ex.what() << " on line:[" << lineNum << "]" << requestMessage);
		return false;
	}

	return true;
}

std::string MidMessage::serialize(const tagMidRequst& request)
{
	std::ostringstream oss;
	// 使用ostringstream进行格式化
	oss << std::setw(4) << std::setfill('0') << request.m_headerLength << "|";
	oss << std::setw(4) << std::setfill('0') << request.m_dataLength << "|";
	oss << request.m_crcCheckCode << "|";
	oss << request.m_versionNumber << "|";
	oss << request.m_userCode << "|";
	oss << request.m_operationSite << "|";
	oss << request.m_accountBranch << "|";
	oss << request.m_operationChannel << "|";
	oss << request.m_sessionNumber << "|";
	oss << request.m_userRole << "|";
	oss << request.m_routeFlag << "|";
	oss << request.m_reqFunctionCode << "|";
	oss << request.requestData << "|";

	// 将ostringstream的内容转换为std::string并返回
	return oss.str();
}

// 解析应答消息
bool MidMessage::ParseAnswerMessage(uint64_t lineNum, const std::string& answerMessage, tagMidAnswer& midAnswer)
{
	if (answerMessage.empty())
		return true;

	std::istringstream ss(answerMessage);
	std::string token;

	try {
		// 解析应答包头
		std::getline(ss, token, '|');
		midAnswer.m_headerLength = std::stoi(token);
		std::getline(ss, token, '|');
		midAnswer.m_dataLength = std::stoi(token);
		std::getline(ss, token, '|');
		midAnswer.m_crcCheckCode = token;
		std::getline(ss, token, '|');
		midAnswer.m_versionNumber = token;
		std::getline(ss, token, '|');
		midAnswer.m_responseCode = token;
		std::getline(ss, token, '|');
		midAnswer.m_responseMessage = token;
		std::getline(ss, token, '|');
		midAnswer.m_morePacketsFlag = token;
		std::getline(ss, token, '|');
		midAnswer.m_respFieldCount = std::stoi(token);
		std::getline(ss, token, '|');
		midAnswer.m_respRecordCount = std::stoi(token);
		std::getline(ss, token, '|');
		midAnswer.m_oriFunctionCode = token;

		// 解析应答数据
		while (std::getline(ss, token, '|'))
		{
			std::string key, value;
			std::istringstream iss(token);
			std::getline(iss, key, '=');
			std::getline(iss, value);
			midAnswer.responseData.push_back(std::make_pair(key, value));
		}
	}
	catch (const std::invalid_argument& ia) {
		LOG4CXX_ERROR(logger, "An exception occurred during anser Analyzing:" << ia.what() << " on line:[" << lineNum << "]" << answerMessage);
		return false;
	}
	catch (const std::out_of_range& oe) {
		LOG4CXX_ERROR(logger, "An exception occurred during anser Analyzing:" << oe.what() << " on line:[" << lineNum << "]" << answerMessage);
		return false;
	}
	catch (const std::exception& ex) {
		LOG4CXX_ERROR(logger, "An exception occurred during anser Analyzing:" << ex.what() << " on line:[" << lineNum << "]" << answerMessage);
		return false;
	}

	return true;
}

// 序列化应答消息
std::string MidMessage::serialize(const tagMidAnswer& answer)
{
	std::ostringstream oss;
	oss << std::setw(4) << std::setfill('0') << answer.m_headerLength << "|";
	oss << std::setw(4) << std::setfill('0') << answer.m_dataLength << "|";
	oss << answer.m_crcCheckCode << "|";
	oss << answer.m_versionNumber << "|";
	oss << answer.m_responseCode << "|";
	oss << std::setw(200) << std::setfill(' ') << answer.m_responseMessage << "|";
	oss << answer.m_morePacketsFlag << "|";
	oss << std::setw(10) << std::setfill('0') << answer.m_respFieldCount << "|";
	oss << std::setw(10) << std::setfill('0') << answer.m_respRecordCount << "|";
	oss << std::setw(20) << std::setfill(' ') << answer.m_oriFunctionCode << "|";
	// 序列化应答数据部分
	for (const auto& pair : answer.responseData) {
		oss << pair.first << "=" << pair.second << "|";
	}
	return oss.str();
}