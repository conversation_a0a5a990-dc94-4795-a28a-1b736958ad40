﻿#include "base.h"
#include "Error.h"
#include <cstdarg>
#include <cstring>

//-------------------------------------------------------------------------
tagErrorMessageList g_ListError[] = {
	{ 0x00000001L, "GEN:Out of Memory!"       },
	{ 0x00000002L, "GEN:Invalid Parameter!"   },
	{ 0x00000003L, "GEN:Invalid Handle!"      },
	{ 0x00000004L, "GEN:Invalid Pointer!"     },
	{ 0x00000005L, "GEN:Invalid Index!"       },
	{ 0x00000006L, "GEN:Invalid Size!"        },
	{ 0x00000007L, "GEN:Invalid Operation!"   },
	{ 0x00000008L, "GEN:Invalid Format!"      },
	{ 0x00000009L, "GEN:Invalid File!"        },
	{ 0x0000000AL, "GEN:Invalid Path!"        },
	{ 0x0000000BL, "GEN:Invalid Name!"        },
	{ 0x0000000CL, "GEN:Invalid Value!"       },
	{ 0x0000000DL, "GEN:Invalid Data!"        },
	{ 0x0000000EL, "GEN:Invalid Type!"        },
	{ 0x0000000FL, "GEN:Invalid State!"       },
	{ 0x00000010L, "GEN:Invalid Flag!"        },
	{ 0x00000011L, "GEN:Invalid Option!"      },
	{ 0x00000012L, "GEN:Invalid Command!"     },
	{ 0x00000013L, "GEN:Invalid Request!"     },
	{ 0x00000014L, "GEN:Invalid Response!"    },
	{ 0x00000015L, "GEN:Invalid Message!"     },
	{ 0x00000016L, "GEN:Invalid Format!"      },
	{ 0x00000017L, "GEN:Invalid Protocol!"    },
	{ 0x00000018L, "GEN:Invalid Version!"     },
	{ 0x00000019L, "GEN:Invalid Header!"      },
	{ 0x0000001AL, "GEN:Invalid Body!"        },
	{ 0x0000001BL, "GEN:Invalid Tail!"        }
};

CError::CError()
{
  m_dwErrorCode = 0;
  m_szErrorMessage[0] = 0;
  m_midPlayerError = MidPlayerError::SUCCESS;
  m_errorContext.clear();
}

CError::~CError()
{
	return;
}

char* CError::GetLastErrorMessage()
{
	//得到错误信息
	return m_szErrorMessage;
}

unsigned int CError::GetLastErrorCode()
{
	//得到错误信息
	return m_dwErrorCode;
}

void CError::SetLastError( unsigned int p_dwErrorCode, const char *p_pszErrorMessageFMT, ...)
{
	//设置最后的错误
	va_list arg_ptr;
	char szTemp[1024]={0};
    
	va_start(arg_ptr, p_pszErrorMessageFMT);
	_vsnprintf(szTemp,sizeof(szTemp) -1, p_pszErrorMessageFMT, arg_ptr);
	va_end(arg_ptr);
	szTemp[sizeof(szTemp) -1] =0;
  
	m_dwErrorCode = p_dwErrorCode;
	strncpy( m_szErrorMessage, szTemp, ERRMSGLEN);
}

const char *CError::QueryErrorMessage(unsigned int dwCode)
{
	//得到错误信息
	int iCount = sizeof(g_ListError)/sizeof(tagErrorMessageList);

	for (int i=0;i<iCount;i++)
	{
		if (g_ListError[i].dwCode==dwCode)
			return g_ListError[i].pszMessage;
	}
	
	m_szErrorMessage[ERRMSGLEN-1] = 0;
	return m_szErrorMessage+ERRMSGLEN-1; //<返回空串
}

// 新的错误处理接口实现
void CError::SetLastError(MidPlayerError errorCode, const std::string& context) {
	m_midPlayerError = errorCode;
	m_errorContext = context;

	// 同时更新旧的错误码以保持兼容性
	m_dwErrorCode = static_cast<unsigned int>(errorCode);

	// 获取格式化的错误消息
	std::string formattedMessage = MidPlayerErrorManager::getInstance().formatError(errorCode, context);
	strncpy(m_szErrorMessage, formattedMessage.c_str(), ERRMSGLEN - 1);
	m_szErrorMessage[ERRMSGLEN - 1] = '\0';
}

MidPlayerError CError::GetLastMidPlayerError() const {
	return m_midPlayerError;
}

std::string CError::GetFormattedErrorMessage() const {
	return MidPlayerErrorManager::getInstance().formatError(m_midPlayerError, m_errorContext);
}

void CError::ClearError() {
	m_dwErrorCode = 0;
	m_szErrorMessage[0] = '\0';
	m_midPlayerError = MidPlayerError::SUCCESS;
	m_errorContext.clear();
}
