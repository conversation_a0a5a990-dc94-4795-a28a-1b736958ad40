﻿#ifndef __OBJECT_POOL_H__
#define __OBJECT_POOL_H__
#pragma once

#include <queue>
#include <mutex>
#include <memory>
#include <functional>
#include <condition_variable>
#include <atomic>

// 通用对象池模板类
template<typename T>
class ObjectPool {
public:
    using ObjectPtr = std::unique_ptr<T>;
    using Factory = std::function<ObjectPtr()>;
    using Resetter = std::function<void(T*)>;
    
    // 构造函数
    ObjectPool(size_t initialSize = 10, size_t maxSize = 100)
        : maxSize_(maxSize), currentSize_(0), destroyed_(false) {
        
        // 默认工厂函数
        factory_ = []() { return std::make_unique<T>(); };
        
        // 默认重置函数（空操作）
        resetter_ = [](T*) {};
        
        // 预分配对象
        for (size_t i = 0; i < initialSize && i < maxSize_; ++i) {
            pool_.push(factory_());
            currentSize_++;
        }
    }
    
    // 带自定义工厂和重置函数的构造函数
    ObjectPool(Factory factory, Resetter resetter = nullptr, 
               size_t initialSize = 10, size_t maxSize = 100)
        : factory_(std::move(factory)), maxSize_(maxSize), currentSize_(0), destroyed_(false) {
        
        if (resetter) {
            resetter_ = std::move(resetter);
        } else {
            resetter_ = [](T*) {};
        }
        
        // 预分配对象
        for (size_t i = 0; i < initialSize && i < maxSize_; ++i) {
            pool_.push(factory_());
            currentSize_++;
        }
    }
    
    ~ObjectPool() {
        destroy();
    }
    
    // 获取对象
    ObjectPtr acquire() {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (destroyed_) {
            return nullptr;
        }
        
        if (!pool_.empty()) {
            auto obj = std::move(pool_.front());
            pool_.pop();
            return obj;
        }
        
        // 如果池为空且未达到最大大小，创建新对象
        if (currentSize_ < maxSize_) {
            currentSize_++;
            lock.unlock();  // 释放锁以避免在工厂函数中死锁
            return factory_();
        }
        
        // 等待对象归还
        condition_.wait(lock, [this] { return !pool_.empty() || destroyed_; });
        
        if (destroyed_) {
            return nullptr;
        }
        
        auto obj = std::move(pool_.front());
        pool_.pop();
        return obj;
    }
    
    // 尝试获取对象（非阻塞）
    ObjectPtr tryAcquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (destroyed_) {
            return nullptr;
        }
        
        if (!pool_.empty()) {
            auto obj = std::move(pool_.front());
            pool_.pop();
            return obj;
        }
        
        // 如果池为空且未达到最大大小，创建新对象
        if (currentSize_ < maxSize_) {
            currentSize_++;
            return factory_();
        }
        
        return nullptr;  // 无可用对象
    }
    
    // 归还对象
    void release(ObjectPtr obj) {
        if (!obj) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (destroyed_) {
            return;
        }
        
        // 重置对象状态
        resetter_(obj.get());
        
        // 归还到池中
        pool_.push(std::move(obj));
        condition_.notify_one();
    }
    
    // 获取池统计信息
    struct PoolStats {
        size_t availableObjects;
        size_t totalObjects;
        size_t maxObjects;
    };
    
    PoolStats getStats() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return {pool_.size(), currentSize_, maxSize_};
    }
    
    // 清空池
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        while (!pool_.empty()) {
            pool_.pop();
        }
        currentSize_ = 0;
    }
    
    // 销毁池
    void destroy() {
        std::lock_guard<std::mutex> lock(mutex_);
        destroyed_ = true;
        clear();
        condition_.notify_all();
    }
    
    // 调整池大小
    void resize(size_t newMaxSize) {
        std::lock_guard<std::mutex> lock(mutex_);
        maxSize_ = newMaxSize;
        
        // 如果新的最大大小小于当前大小，移除多余的对象
        while (pool_.size() > newMaxSize && !pool_.empty()) {
            pool_.pop();
            currentSize_--;
        }
    }
    
private:
    mutable std::mutex mutex_;
    std::condition_variable condition_;
    std::queue<ObjectPtr> pool_;
    Factory factory_;
    Resetter resetter_;
    size_t maxSize_;
    std::atomic<size_t> currentSize_;
    bool destroyed_;
};

// RAII对象包装器 - 自动归还对象到池
template<typename T>
class PooledObject {
public:
    PooledObject(std::unique_ptr<T> obj, ObjectPool<T>* pool)
        : object_(std::move(obj)), pool_(pool) {}
    
    ~PooledObject() {
        if (object_ && pool_) {
            pool_->release(std::move(object_));
        }
    }
    
    // 禁止拷贝
    PooledObject(const PooledObject&) = delete;
    PooledObject& operator=(const PooledObject&) = delete;
    
    // 允许移动
    PooledObject(PooledObject&& other) noexcept
        : object_(std::move(other.object_)), pool_(other.pool_) {
        other.pool_ = nullptr;
    }
    
    PooledObject& operator=(PooledObject&& other) noexcept {
        if (this != &other) {
            if (object_ && pool_) {
                pool_->release(std::move(object_));
            }
            object_ = std::move(other.object_);
            pool_ = other.pool_;
            other.pool_ = nullptr;
        }
        return *this;
    }
    
    T* get() const { return object_.get(); }
    T& operator*() const { return *object_; }
    T* operator->() const { return object_.get(); }
    
    explicit operator bool() const { return static_cast<bool>(object_); }
    
private:
    std::unique_ptr<T> object_;
    ObjectPool<T>* pool_;
};

// 便捷函数 - 创建RAII包装的对象
template<typename T>
PooledObject<T> makePooledObject(ObjectPool<T>& pool) {
    auto obj = pool.acquire();
    return PooledObject<T>(std::move(obj), &pool);
}

template<typename T>
PooledObject<T> tryMakePooledObject(ObjectPool<T>& pool) {
    auto obj = pool.tryAcquire();
    return PooledObject<T>(std::move(obj), &pool);
}

#endif // __OBJECT_POOL_H__
