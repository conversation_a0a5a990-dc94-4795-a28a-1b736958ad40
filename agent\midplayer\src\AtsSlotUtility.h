﻿#ifndef __MID_SLOTHASH_H__
#define __MID_SLOTHASH_H__
#pragma once

struct midRow {
	uint64_t lineNum;
	uint64_t retryCount;
	std::string lineuser;
	std::string lineText;
};

struct delayRow {
	uint64_t lineNum;
	orderMap delayText;
};

inline std::string getCurrentTime(bool dashfmt = true, bool bracket = true) {
	auto now = std::chrono::system_clock::now();
	std::time_t now_time = std::chrono::system_clock::to_time_t(now);
	std::tm now_tm;
	localtime_s(&now_tm, &now_time);
	std::ostringstream oss;
	if (!dashfmt)
		if (!bracket)
			oss << std::put_time(&now_tm, "%Y%m%d%H%M%S");
		else
			oss << std::put_time(&now_tm, "[%Y%m%d%H%M%S]");
	else
		if (!bracket)
			oss << std::put_time(&now_tm, "%Y-%m-%d %H:%M:%S");
		else
			oss << std::put_time(&now_tm, "[%Y-%m-%d %H:%M:%S]");
	return oss.str();
}

// 实现一个简单的定制化哈希映射，其中每个线程有固定数量的虚拟槽，并且每个槽位根据客户号哈希散列的结果来分配，需要调整一致性哈希的实现以支持这种结构，
// 为每个线程预先分配固定数量的虚拟槽，并根据客户号的哈希值将请求映射到这些槽位上，最后再将槽位映射到实际的线程。
class SlotHashSlover {
public:
	SlotHashSlover(size_t num_threads, size_t slots_per_thread)
		: in_num_threads(num_threads), in_slots_per_thread(slots_per_thread) {
		// 初始化虚拟槽
		for (size_t i = 0; i < num_threads; ++i)
			for (size_t j = 0; j < slots_per_thread; ++j)
				virtual_slots.push_back(i);
	}

	size_t getThreadForCustomer(const std::string& user_code) const {
		// 使用标准库的哈希函数
		size_t hash = std::hash<std::string>{}(user_code);
		size_t slot = hash % virtual_slots.size();
		return virtual_slots[slot];
	}

private:
	size_t in_num_threads;
	size_t in_slots_per_thread;
	std::vector<size_t> virtual_slots;
};

class SlotHashRing {
public:
	SlotHashRing(size_t num_threads) : in_num_threads(num_threads), thread_loads(num_threads) {
		for (auto& load : thread_loads)
			load.store(0);
	}

	size_t getLeastLoadedThread() {
		return std::min_element(thread_loads.begin(), thread_loads.end()) - thread_loads.begin();
	}

	size_t getThreadForCustomer(const std::string& user_code) {
		if (!user_code.empty()) {
			// 检查客户是否已经分配了线程
			auto it = user_request_thread.find(user_code);
			if (it != user_request_thread.end()) {
				thread_loads[it->second]++;
				return it->second;
			}
		}

		// 动态负载均衡：选择负载最轻的线程
		size_t alloc_thread_id = getLeastLoadedThread();
		thread_loads[alloc_thread_id]++;
		user_request_thread[user_code]= alloc_thread_id;
		return alloc_thread_id;
	}

	void subUserDone(size_t thread_id) {
		thread_loads[thread_id]--;
	}

	void resetBalance() {
		user_request_thread.clear();
		for (auto& load : thread_loads)
			load.store(0);
	}

	void redoBalance() {
		for (auto it = user_request_thread.begin(); it != user_request_thread.end(); ) {
			size_t thread_id = it->second;
			if (thread_loads[thread_id] == 0) {
				it = user_request_thread.erase(it);
			}
			else {
				++it;
			}
		}
	}

	void adjustBalance(std::vector<std::queue<midRow>>& queue_midrows, std::vector<std::mutex>& queue_mutexes, std::vector<std::condition_variable>& cv_queue_not_empty) {
		// 创建一个包含线程索引和负载的向量
		std::vector<std::pair<size_t, size_t>> load_indices;
		for (size_t i = 0; i < thread_loads.size(); ++i) {
			load_indices.emplace_back(i, thread_loads[i].load());
		}

		// 按照负载从大到小排序
		std::sort(load_indices.begin(), load_indices.end(), [](const std::pair<size_t, size_t>& a, const std::pair<size_t, size_t>& b) {
			return a.second > b.second;
		});

		// 分组：高负载线程和低负载线程
		std::vector<size_t> high_load_threads, low_load_threads;
		for (size_t i = 0; i < load_indices.size() / 2; ++i) {
			high_load_threads.push_back(load_indices[i].first);
		}
		for (size_t i = load_indices.size() / 2; i < load_indices.size(); ++i) {
			low_load_threads.push_back(load_indices[i].first);
		}

		// 反转低负载线程的顺序，使其与高负载线程一一对应
		std::reverse(low_load_threads.begin(), low_load_threads.end());

		// 数据迁移，负载差异阈值
		size_t max_transferred = 0;
		const size_t load_difference_threshold = 30;

		for (size_t i = 0; i < high_load_threads.size(); ++i) {
			size_t high_thread = high_load_threads[i];
			size_t low_thread = low_load_threads[i];

			// 锁定高负载和低负载的线程
			std::unique_lock<std::mutex> high_load_lock(queue_mutexes[high_thread], std::defer_lock);
			std::unique_lock<std::mutex> low_load_lock(queue_mutexes[low_thread], std::defer_lock);
			std::lock(high_load_lock, low_load_lock);

			// 只有当负载差异超过阈值时才进行数据迁移
			if (thread_loads[high_thread] > thread_loads[low_thread] + load_difference_threshold) {
				std::unordered_map<std::string, size_t> user_request_count;
				std::queue<midRow> temp_queue;

				// 统计每个用户的请求数量
				while (!queue_midrows[high_thread].empty()) {
					midRow data = queue_midrows[high_thread].front();
					queue_midrows[high_thread].pop();
					user_request_count[data.lineuser]++;
					temp_queue.push(data);
				}

				// 将临时队列中的数据放回高负载线程的队列
				queue_midrows[high_thread] = std::move(temp_queue);
				if (1 == user_request_count.size())
					continue;

				// 找到请求数量最多的用户
				auto max_user_it = std::max_element(user_request_count.begin(), user_request_count.end(),
					[](const std::pair<std::string, size_t>& a, const std::pair<std::string, size_t>& b) {
						return a.second < b.second;
				});
				if (max_user_it == user_request_count.end() || max_user_it->second == 0)
					continue;

				std::string user_code = max_user_it->first;
				size_t transferred = 0;

				// 将该用户的所有请求转移到低负载线程
				std::queue<midRow>& high_queue = queue_midrows[high_thread];
				std::queue<midRow>& low_queue = queue_midrows[low_thread];
				temp_queue = std::queue<midRow>();

				while (!high_queue.empty()) {
					midRow data = high_queue.front();
					if (data.lineuser == user_code) {
						transferred++;
						low_queue.push(data);
					}
					else {
						temp_queue.push(data);
					}
					high_queue.pop();
				}

				high_queue = std::move(temp_queue);

				// 更新客户到线程的映射
				thread_loads[high_thread] -= transferred;
				thread_loads[low_thread] += transferred;
				user_request_thread[user_code] = low_thread;

				// 打印线程负载变化情况
				if (max_transferred < transferred) max_transferred = transferred;
				LOG4CXX_INFO(logger, "用户[" << user_code << "]从线程[" << high_thread << "]迁移到[" << low_thread << "]数量 " << transferred);

				// 解锁互斥锁
				high_load_lock.unlock();
				low_load_lock.unlock();
			}
		}

		// 一次性通知低负载线程有新数据可处理
		for (size_t j = 0; j < max_transferred; ++j) {
			cv_queue_not_empty[0].notify_one();
		}
	}

public:
	size_t in_num_threads;
	std::vector<std::atomic<size_t>> thread_loads;
	std::unordered_map<std::string, size_t> user_request_thread;
};

#endif