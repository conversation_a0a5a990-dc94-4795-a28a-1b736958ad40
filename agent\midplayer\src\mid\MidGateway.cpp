﻿#include "stdafx.h"
#include "crc.h"
#include "Blowfish.h"
#include "MiniSocket.h"
#include "MidGateway.h"
/*
* MidMessage是表示MID协议中的消息的类
* 协议描述：
* 第三方网关必须以局域网方式或数据专线方式接入KDMID三方网关.不允许将KDMID端口直接暴露在公共网络上。请求包数据域中不能包含符号‘，’后台BP的LBM处理视‘，’为每一个字段数据的结束符号。
* 第三方网关采用TCP通讯协议与KDMID建立多个连接。每个连接采用同步一问一答方式交互,发送请求数据包后,必须等待应答数据包数据,方可发起下一个请求。
*/
/*
* 协议包逻辑结构概述:
* 1、请求包定义：所有数据报文被定义为一串字符集。在这一字符串集中，各字符串均需以“|”字符为分隔符，每个数据包最后也用“|”结束。如果前台数据字符串中含有'|'字符或中文字符中有0X7C，则转义成'&#124;'发往后台。
* 请求包由两部分组成：请求包头和请求数据；请求包头结构如下：
* 域名---------说明
* 头长度			CAHR(4),前面以字符'0'填充，请求包头长度指“请求包头”的字节长度
* 数据域长度		CHAR(4),前面以字符'0'填充，指“请求数据”的长度
* CRC校验码		CHAR(8), 见注4,7
* 版本号			当前协议版本编号(固定串“KDGATEWAY1.2”)
* 用户代码		CHAR(10), 登陆后送，为操作者代码
* 操作站点		CHAR(64)
* 开户分支		CHAR(6), 登陆后送
* 操作渠道		CHAR(1), 见数据字典
* 会话序号		CHAR(16), 登陆后送
* 保留字段1		CHAR(20), 用户角色 OP_ROLE(2006-4-12) 注9
* 保留字段2		CHAR(20), 请求包序列号    (2006-4-12)
* 保留字段3		CHAR(20), 多柜台转发路由标记，见网关CONFIG.INI的配置
*
* 请求数据结构如下：
* 请求数据=请求功能号(CHAR(5)) + 请求数据域
*
* 2、应答包定义：所有数据报文被定义为一串字符集。在这一字符串集中，各字符串均需以“|”字符为分隔符，每个数据包最后也用“|”结束。如果后台数据字符串中含有'&#124;'则转义成'|'。
* 应答包由两部分组成：应答包头和应答数据；应答包头结构如下：
* 域名---------说明
* 头长度			CHAR(4),前面以字符'0'填充
* 数据长度		CHAR(N),前面以字符'0'填充
* CRC校验码		CHAR(8), 见注5,7
* 版本号			当前协议版本编号(固定串“KDGATEWAY1.2”)
* 返回码			CHAR(10), “0”表示正常
* 返回信息		CHAR(200),返回非0则表示交易处理出现某种交易错误或系统错误(见数据字典)
* 后续包标示		CHAR(1),'0'－无，'1'－表示有后续包(取后续包发99请求)
* 应答字段数		CHAR(10)
* 应答记录数		CHAR(10)
* 原请求功能号	CHAR(20), 2006-4-12增加
* 请求包序列号	CHAR(20), 2006-4-12增加
* 保留字段2		CHAR(20), 2006-4-12增加
*/
#define KDGW_KDG	"kdg_std"
#define KDGW_TDX	"kdg_tdx"
#define KDGW_SVR	"server"
#define KDGW_PRT	"port"
#define KDGW_TMT	"timeout"

// 静态互斥锁
std::mutex MidGateway::g_dataMutex;
std::map<uint64_t, std::string> MidGateway::g_anserData;

/////////////////////////////////////////////////////////////////////////////
// MidGateway 金证新一代网关接口
//---------------------------------------------------------------------------
MidGateway::MidGateway()
{
    //初始化所有运行变量
    memset(m_opUser, 0, DEF_SBUFLEN);   //操作员代码
    memset(m_opRole, 0, DEF_SBUFLEN);   //操作角色
    memset(m_opSite, 0, DEF_SBUFLEN);   //操作站点
    memset(m_opBranch, 0, DEF_SBUFLEN);   //操作分支
    memset(m_opChannel, 0, DEF_SBUFLEN);   //操作渠道
    memset(m_opRouter, 0, DEF_SBUFLEN);
    memset(m_szVersion, 0, DEF_SBUFLEN);   //接口版本号:"KDGATEWAY1.2"
    memset(m_crcCode, 0, DEF_SBUFLEN);   //CRC校验
    memset(m_workKey, 0, DEF_SBUFLEN);   //工作密钥
    memset(m_opSession, 0, DEF_SBUFLEN);   //通讯Session
    memset(m_szRequestBuffer, 0, DEF_LBUFLEN_8192); //请求缓冲区

    m_dwReqPakSN = 0;
    m_pSocket = NULL;
    SetLastError(0, "OK.");

    strcpy(m_szVersion, "KDGATEWAY1.2");//接口版本号
    strcpy(m_crcCode, "12345678");//CRC校验
    strcpy(m_workKey, "12345678");//工作密钥

    m_singleRecvBufferSize = 64 * 1024L;
    m_singleRecvBuffer = new char[m_singleRecvBufferSize];

    m_dwDataBufferSize = 512 * 1024L;
    m_returnDataBuffer = new char[m_dwDataBufferSize];
    memset(m_returnDataBuffer, 0, m_dwDataBufferSize);

    m_dwTotalDataLen = 0;
    m_dwRecordNo = 0;
    memset(m_responDataBuffer, 0, sizeof(m_responDataBuffer));
}

//---------------------------------------------------------------------------
MidGateway::~MidGateway()
{
    if (m_pSocket != NULL)
        delete m_pSocket;
}

bool MidGateway::connectToMidStdGW()
{
	if (!appConfig.configers[KDGW_KDG].contains(KDGW_SVR))
        appConfig.configers[KDGW_KDG][KDGW_SVR] = "***********";
	if (!appConfig.configers[KDGW_KDG].contains(KDGW_PRT))
		appConfig.configers[KDGW_KDG][KDGW_PRT] = "9100";
	if (!appConfig.configers[KDGW_KDG].contains(KDGW_TMT))
		appConfig.configers[KDGW_KDG][KDGW_TMT] = "15";

    const char* serverAddress = appConfig.configers[KDGW_KDG][KDGW_SVR].c_str();
	WORD serverPort = std::atoi(appConfig.configers[KDGW_KDG][KDGW_PRT].c_str());
    WORD timeOut = std::atoi(appConfig.configers[KDGW_KDG][KDGW_TMT].c_str());

    if (!connectToServer(serverAddress, serverPort, timeOut))
        return false;

    setParams(appConfig.checkins.c_str());
    if (!signSystem()) return false;
	if (!readySystem()) return false;

    return true;
}

bool MidGateway::connectToMidTdxGW()
{
	if (!appConfig.configers[KDGW_TDX].contains(KDGW_SVR))
		appConfig.configers[KDGW_TDX][KDGW_SVR] = "***********";
	if (!appConfig.configers[KDGW_TDX].contains(KDGW_PRT))
		appConfig.configers[KDGW_TDX][KDGW_PRT] = "9200";
	if (!appConfig.configers[KDGW_TDX].contains(KDGW_TMT))
		appConfig.configers[KDGW_TDX][KDGW_TMT] = "15";

	const char* serverAddress = appConfig.configers[KDGW_TDX][KDGW_SVR].c_str();
	WORD serverPort = std::atoi(appConfig.configers[KDGW_TDX][KDGW_PRT].c_str());
	WORD timeOut = std::atoi(appConfig.configers[KDGW_TDX][KDGW_TMT].c_str());

	if (!connectToServer(serverAddress, serverPort, timeOut))
		return false;

	setParams(appConfig.checkins.c_str());
	if (!signSystem()) return false;

    return true;
}

//---------------------------------------------------------------------------
//与KDGateway建立连接
bool MidGateway::connectToServer(const char* serverAddress, WORD serverPort, int timeOut)
{
    if (m_pSocket == NULL) {//创建通讯类
        m_pSocket = new CMiniSocket;
        if (m_pSocket == NULL) {
            SetLastError(-1, "创建通讯类失败");
            return false;
        }
    }
    //关闭可能的连接
    if (m_pSocket->getSocket() != INVALID_SOCKET)
        m_pSocket->closeSocket();

    if (m_pSocket->connectToServer((char*)serverAddress, serverPort, timeOut) != 0) {
        SetLastError(-2, "连接外围网关失败！");
        return false;
    }
    return true;
}

//与KDGateway断开连接
//---------------------------------------------------------------------------
bool MidGateway::disconnect()
{
    if (m_pSocket == NULL) {
        SetLastError(-3, "尚未成功连接不能 Disconnect！");
        return false;
    }

    //关闭可能的连接
    if (m_pSocket->getSocket() != INVALID_SOCKET)
        m_pSocket->closeSocket();

    delete m_pSocket;
    m_pSocket = NULL;
    return true;
}

bool MidGateway::reConnect()
{
    // 关闭连接
    disconnect();
    // 重新签入
    return connectToMidStdGW();
}

//---------------------------------------------------------------------------
//发送请求包
bool MidGateway::sendMessage(const char* message)
{
    if (m_pSocket == NULL) {
        SetLastError(-3, "尚未成功连接KDGATEWAY 不能发送请求！");
        return false;
    }

    m_pSocket->clearBuffer(); //因为是同步模式，所以在发送前把接收缓冲区清空，以防止收到错乱包，导致接收数据出错

    //在请求序列包内字段内填入 线程ID:请求包数
    m_dwReqPakSN++;

    char szReqPakSN[DEF_SBUFLEN] = { 0 };
    sprintf(szReqPakSN, "%x:%x", GetCurrentThreadId(), m_dwReqPakSN);

    sprintf(m_szRequestBuffer, "%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|",
        "0000", //包头大小
        "0000", //数据大小
        m_workKey, //CRC
        m_szVersion,
        m_opUser,
        m_opSite,
        m_opBranch,
        m_opChannel,
        m_opSession,
        m_opRole,
        szReqPakSN,
        m_opRouter
    );

    // sprintf(m_szRequestBuffer,"0056|0004|%s|KDGATEWAY1.2||127.0.0.1||7|||%s|74|",m_szWorkKey,szReqPakSN);

    int iHeadLen;
    int iDataLen;
    char szTemp[DEF_SBUFLEN] = { 0 };

    //写入包头长度
    sprintf(szTemp, "%04lld", strlen(m_szRequestBuffer));
    memcpy(m_szRequestBuffer, szTemp, 4);
    iHeadLen = atoi(szTemp);

    //写入请求段长度
    sprintf(szTemp, "%04lld", strlen(message) + 1); //加1是为了增加数据包内的最后一个'|'
    memcpy(m_szRequestBuffer + 5, szTemp, 4);
    iDataLen = atoi(szTemp);

    strcat(m_szRequestBuffer, message);

    // for test
    //if (strncmp(p_pszDataSect,"301",3)!=0)
    strcat(m_szRequestBuffer, "|");

    //设置CRC校验码
    char szCRCCode[DEF_SBUFLEN] = { 0 };
    memset(szCRCCode, 0, DEF_SBUFLEN);
    CRC_Get((unsigned char*)m_szRequestBuffer, iHeadLen + iDataLen, (unsigned char*)szCRCCode);
    szCRCCode[8] = 0;
    memcpy(m_szRequestBuffer + 10, szCRCCode, 8);

    if (m_btraceon) {
        MFLOG_DEBUG("SEND=[{}]\n", m_szRequestBuffer);
    }

    if (m_pSocket->sendBuffer(m_szRequestBuffer, (int)strlen(m_szRequestBuffer)) <= 0) {
        SetLastError(-2, "发送请求包失败!");
        return false;
    }

    memset(m_responDataBuffer, 0, sizeof(m_responDataBuffer));

    return true;
}


bool MidGateway::sendRequestOpt(std::vector<std::string> vecData)
{
    // 1 判断socket
    if (m_pSocket == NULL) {
        SetLastError(-3, "尚未成功连接KDGATEWAY 不能发送请求！");
        return false;
    }
    m_pSocket->clearBuffer(); //因为是同步模式，所以在发送前把接收缓冲区清空，以防止收到错乱包，导致接收数据出错

    // 2 组包头
    //在请求序列包内字段内填入 线程ID:请求包数
    m_dwReqPakSN++;
    char szReqPakSN[DEF_SBUFLEN] = { 0 };
    sprintf(szReqPakSN, "%x:%x", GetCurrentThreadId(), m_dwReqPakSN);

    sprintf(m_szRequestBuffer, "%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|",
        "0000", //包头大小
        "0000", //数据大小
        m_workKey, //CRC
        vecData[3].c_str(),
        vecData[4].c_str(),
        vecData[5].c_str(),
        vecData[6].c_str(),
        vecData[7].c_str(),
        vecData[8].c_str(),
        vecData[9].c_str(),
        szReqPakSN,
        vecData[11].c_str()
    );

    // sprintf(m_szRequestBuffer,"0056|0004|%s|KDGATEWAY1.2||127.0.0.1||7|||%s|74|",m_szWorkKey,szReqPakSN);
    // 写入包头长度
    int iHeadLen;
    int iDataLen;
    char szTemp[DEF_SBUFLEN] = { 0 };
    sprintf(szTemp, "%04lld", strlen(m_szRequestBuffer));
    memcpy(m_szRequestBuffer, szTemp, 4);
    iHeadLen = atoi(szTemp);

    // 3 组数据域
    char p_pszDataSect[DEF_LBUFLEN_8192] = { 0 };
    std::string strTemp = "";
    for (int i = 12; i < vecData.size(); ++i) {
        // 12 为第一个字段，即功能号 
        if (i == 12) {
            strTemp = strTemp + vecData[i];
        }
        else {
            strTemp = strTemp + "|" + vecData[i];
        }

    }
    strcpy(p_pszDataSect, strTemp.c_str());

    //写入请求段长度
    sprintf(szTemp, "%04lld", strlen(p_pszDataSect) + 1); //加1是为了增加数据包内的最后一个'|'
    memcpy(m_szRequestBuffer + 5, szTemp, 4);
    iDataLen = atoi(szTemp);

    // 4 head + data + '|'
    strcat(m_szRequestBuffer, p_pszDataSect);
    strcat(m_szRequestBuffer, "|");

    // 5 设置CRC校验码
    char szCRCCode[DEF_SBUFLEN] = { 0 };
    memset(szCRCCode, 0, DEF_SBUFLEN);
    CRC_Get((unsigned char*)m_szRequestBuffer, iHeadLen + iDataLen, (unsigned char*)szCRCCode);
    szCRCCode[8] = 0;
    memcpy(m_szRequestBuffer + 10, szCRCCode, 8);

    if (m_btraceon) {
        MFLOG_DEBUG("SEND=[{}]\n", m_szRequestBuffer);
    }

    if (m_pSocket->sendBuffer(m_szRequestBuffer, (int)strlen(m_szRequestBuffer)) <= 0) {
        SetLastError(-2, "发送请求包失败!");
        return false;
    }

    memset(m_responDataBuffer, 0, sizeof(m_responDataBuffer));

    return true;
}
//---------------------------------------------------------------------------
//Recv接收请答包
bool MidGateway::recvtagMessage(tagAnswerPacket* answerPacket, int crcCode)
{
    int iLen;
    int iPos;
    int iHeadLen;
    DWORD dwDataLen;
    char szTemp[DEF_SBUFLEN] = { 0 };

    //先收包头的4位，得到包头大小
    iPos = 0;
    iLen = 4;
    if (m_pSocket->receiveBuffer(m_singleRecvBuffer + iPos, iLen) != iLen) {
        SetLastError(-4, "接收应答包错误！No.1");
        return false;
    }
    iPos += iLen;

    m_singleRecvBuffer[iPos] = 0;
    iHeadLen = atoi(m_singleRecvBuffer);
    if ((iHeadLen > 2048) || (iHeadLen < 20)) {
        m_pSocket->clearBuffer(); //出现这种错误可能是因为错位，所以缓冲区内的数据全部清掉

        SetLastError(-5, "应答包头尺寸非法，可能是错包！ size=%d", iHeadLen);
        return false;
    }

    //再取整个包头
    iLen = iHeadLen - iPos;
    if (m_pSocket->receiveBuffer(m_singleRecvBuffer + iPos, iLen) != iLen) {
        SetLastError(-6, "接收应答包错误！No.2");
        return false;
    }
    iPos += iLen;
    if (m_responDataBuffer[0] == 0) {
        // MFLOG_DEBUG("Get last error");
        memcpy(m_responDataBuffer, m_singleRecvBuffer, iPos);

        char szerrno[32] = { 0 };
        char szerrmsg[256] = { 0 };
        ats::GetValue(m_singleRecvBuffer, szerrno, 5, '|', sizeof(szerrno) - 1);
        ats::GetValue(m_singleRecvBuffer, szerrmsg, 6, '|', sizeof(szerrmsg) - 1);
        //printf("errmsg=%s",szerrmsg);

        MFLOG_DETAIL("ErrCode:{} ErrMsg:{}", szerrno, szerrmsg);
        SetLastError(atoi(szerrno), szerrmsg);
    }

    if (m_btraceon) {
        MFLOG_DEBUG("RECV=[{}]\n", m_singleRecvBuffer);
    }

    m_singleRecvBuffer[iPos + iLen] = 0;

    //包头取到后，判断序列号字段，以避免串户情况
    char szReqPakSN[DEF_SBUFLEN] = { 0 };
    sprintf(szReqPakSN, "%x:%x", GetCurrentThreadId(), m_dwReqPakSN);

    ats::GetValue(m_singleRecvBuffer, szTemp, 11, '|', DEF_SBUFLEN);

    //if (strcmp(szReqPakSN, szTemp) != 0) {
    //    MFLOG_ERR("应答包非请求包数据。szReqPakSN={} szTemp=[{}] receiveLength=[{}]", szReqPakSN, szTemp, m_pSocket->receiveLength());
    //    m_pSocket->clearBuffer(); //出现这种错误可能是因为错位，所以缓冲区内的数据全部清掉
    //    SetLastError(-6, "应答包非请求包数据。[%s]", m_pSingleRecvBuffer);
    //    return false;
    //}

    //得到数据区大小
    ats::GetValue(m_singleRecvBuffer, szTemp, 2, '|', DEF_SBUFLEN);
    dwDataLen = atol(szTemp);

    m_singleRecvBuffer[iPos + dwDataLen] = 0;
    if (dwDataLen != 0) {//如果出错的话，是不会有数据段的
        //再接收整个数据段
        iLen = dwDataLen;
        if (iPos + iLen > (m_singleRecvBufferSize - iHeadLen)) {
            SetLastError(-7, "接收应答包错误！单包太大! (包长：%d)", iLen);
            return false;
        }
        if (m_pSocket->receiveBuffer(m_singleRecvBuffer + iPos, iLen) != iLen) {
            SetLastError(-7, "接收应答包错误！No.3 (包长：%d)", iLen);
            return false;
        }
        iPos += dwDataLen;
    }

    //判断是否还有因异常产生的剩余数据，如果有就清掉，为下一次接收做准备，因为协议依赖顺序关系
    if (m_pSocket->receiveLength() != 0) {
        m_pSocket->clearBuffer();
        MFLOG_DEBUG("警告：接收缓冲区还有剩余数据！做清除处理");
    }

    if (crcCode) {
        //检查CRC校验码
        char szCRCCode_Answer[DEF_SBUFLEN] = { 0 };
        char szCRCCode[DEF_SBUFLEN] = { 0 };

        memset(szCRCCode_Answer, 0, DEF_SBUFLEN);
        memset(szCRCCode, 0, DEF_SBUFLEN);

        char* pCRC;
        pCRC = strchr(m_singleRecvBuffer, '|');
        if (pCRC == NULL) { //CRC校验码失败！
            SetLastError(-10, "包格式非法1，缺少|");
            return false;
        }
        pCRC++;
        pCRC = strchr(pCRC, '|');
        if (pCRC == NULL) { //CRC校验码失败！
            SetLastError(-10, "包格式非法2，缺少|");
            return false;
        }
        pCRC++;
        strncpy(szCRCCode_Answer, pCRC, 8);  //备份以前的CRC码

        memcpy(pCRC, m_workKey, 8); //写入工作密钥
        CRC_Get((unsigned char*)m_singleRecvBuffer, iHeadLen + dwDataLen, (unsigned char*)szCRCCode);
        szCRCCode[8] = 0;
        if (strncmp(szCRCCode, szCRCCode_Answer, 8) != 0) { //CRC校验码失败！
            SetLastError(-8, "CRC校验失败！");
            return false;
        }
    }

    ats::GetValue(m_singleRecvBuffer, answerPacket->szVersion, 4, '|', DEF_SBUFLEN);
    ats::GetValue(m_singleRecvBuffer, answerPacket->szRetCode, 5, '|', DEF_SBUFLEN);
    ats::GetValue(m_singleRecvBuffer, answerPacket->szRetMsg, 6, '|', DEF_LBUFLEN_8192);

    ats::GetValue(m_singleRecvBuffer, szTemp, 7, '|', DEF_SBUFLEN);//是否有下条记录
    answerPacket->bIsNext = atoi(szTemp);

    ats::GetValue(m_singleRecvBuffer, szTemp, 8, '|', DEF_SBUFLEN);//字段数量
    answerPacket->iFieldNum = atoi(szTemp);

    ats::GetValue(m_singleRecvBuffer, szTemp, 9, '|', DEF_SBUFLEN);//记录数量
    answerPacket->iRecNum = atoi(szTemp);

    answerPacket->iHeadLen = iHeadLen;
    answerPacket->dwDataLen = dwDataLen;

    if (m_dwTotalDataLen < (m_dwDataBufferSize - dwDataLen)) {//数据可以放到缓冲区内
        m_dwRecordNo += answerPacket->iRecNum;
        memcpy(m_returnDataBuffer + m_dwTotalDataLen, m_singleRecvBuffer + iHeadLen, dwDataLen);
        m_dwTotalDataLen += dwDataLen;
        *(m_returnDataBuffer + m_dwTotalDataLen) = 0;
    }
    return true;
}

bool MidGateway::receiveMessage(MidRespRecord& record, uint64_t num)
{
	int length = 4;
	int position = 0;

    //先取包头大小
    if (!m_pSocket->receiveBuffer(m_singleRecvBuffer + position, length)) {
		logErrorMessage(-1, "[line:%llu]接收MID应答包错误！No.1", num);
		return false;
	}
	position += length;

	m_singleRecvBuffer[position] = 0;
    int headerLength = atoi(m_singleRecvBuffer);
	if ((headerLength > 2048) || (headerLength < 20)) {
		m_pSocket->clearBuffer(3);
		//出现这种错误可能是因为错位，所以缓冲区内的数据全部清掉
		logErrorMessage(-2, "[line:%llu]MID应答包头尺寸非法，可能是错包！", num);
		return false;
	}

	//再取整个包头
	length = headerLength - position;
	if (!m_pSocket->receiveBuffer(m_singleRecvBuffer + position, length)) {
		logErrorMessage(-3, "[line:%llu]接收MID应答包错误！No.2", num);
		return false;
	}
	position += length;
    m_singleRecvBuffer[position + length] = 0;

	//得到数据区大小
    std::string datafiled;
    extract(m_singleRecvBuffer, 2, datafiled);

	int dataLength = std::atol(datafiled.c_str());
	m_singleRecvBuffer[position + dataLength] = 0;

	if (dataLength != 0)
	{
		length = dataLength;
		if (position + length > (m_singleRecvBufferSize - headerLength))
		{
            logErrorMessage(-7, "接收应答包错误！单包太大! (包长：%d)", length);
			return FALSE;
		}

		if (m_pSocket->receiveBuffer(m_singleRecvBuffer + position, length) != length)
		{
            logErrorMessage(-7, "接收应答包错误！No.3 (包长：%d)", length);
			return FALSE;
		}
        position += dataLength;
	}

	//判断是否还有因异常产生的剩余数据，如果有就清掉，为下一次接收做准备，因为协议依赖顺序关系
	if (m_pSocket->receiveLength() != 0)
	{
		m_pSocket->clearBuffer();
        logErrorMessage("警告：接收缓冲区还有剩余数据！做清除处理");
	}

    return parseResponse(m_singleRecvBuffer, position, record);
}

bool MidGateway::receiveMessage(std::string& reqsession, MidRespRecord& record, uint64_t num)
{
	int length = 4;
	int position = 0;

	//先取包头大小
	if (!m_pSocket->receiveBuffer(m_singleRecvBuffer + position, length)) {
		logErrorMessage(-1, "[line:%llu]接收MID应答长度失败，可能请求数据包有误！", num);
		return false;
	}
	position += length;

	m_singleRecvBuffer[position] = 0;
	int headerLength = atoi(m_singleRecvBuffer);
	if ((headerLength > 2048) || (headerLength < 20)) {
		m_pSocket->clearBuffer(3);
		//出现这种错误可能是因为错位，所以缓冲区内的数据全部清掉
		logErrorMessage(-2, "[line:%llu][%d]接收MID应答尺寸非法，可能是错包！", num, headerLength);
		return false;
	}

	//再取整个包头
	length = headerLength - position;
	if (!m_pSocket->receiveBuffer(m_singleRecvBuffer + position, length)) {
		logErrorMessage(-3, "[line:%llu][%d]接收MID应答报头错误！No.2", num, headerLength);
		return false;
	}
	position += length;
	m_singleRecvBuffer[position + length] = 0;

	//得到数据区大小
	std::string datafiled, anssession;
	extract(m_singleRecvBuffer, 2, datafiled);
	extract(m_singleRecvBuffer, 11, anssession);

    if (anssession != reqsession) {
		logErrorMessage(-4, "[line:%llu]接收MID应答包非请求包数据！No.3", num);
        return receiveMessage(record, num);
    }

	int dataLength = std::atol(datafiled.c_str());
	m_singleRecvBuffer[position + dataLength] = 0;

	if (dataLength != 0)
	{
		length = dataLength;
		if (position + length > (m_singleRecvBufferSize - headerLength))
		{
			logErrorMessage(-7, "接收应答包错误！单包太大! (包长：%d)", length);
			return FALSE;
		}

		if (m_pSocket->receiveBuffer(m_singleRecvBuffer + position, length) != length)
		{
			logErrorMessage(-7, "接收应答包错误！No.3 (包长：%d)", length);
			return FALSE;
		}
		position += dataLength;
	}

	//判断是否还有因异常产生的剩余数据，如果有就清掉，为下一次接收做准备，因为协议依赖顺序关系
	if (m_pSocket->receiveLength() != 0)
	{
		m_pSocket->clearBuffer();
		logErrorMessage("警告：接收缓冲区还有剩余数据！做清除处理");
	}

	return parseResponse(m_singleRecvBuffer, position, record);
}

bool MidGateway::parseResponse(const char* str, int len, MidRespRecord& record)
{
	std::string token;
	size_t fieldIndex = 0;
	std::istringstream iss(str, len);

    while (std::getline(iss, token, '|') /*&& !token.empty()*/)
    {
        // 替换转义字符'&#124;'为'|'
        if (token == "&#124;")
            token = "|";

        switch (fieldIndex++) {
        case 0: // 头长度
            record.headerLength = token;
            break;
        case 1: // 数据长度
            record.dataLength = token;
            break;
        case 2: // CRC校验码
            record.crcValue = token;
            break;
        case 3: // 版本号
            record.protoVersion = token;
            break;
        case 4: // 返回码
            record.respCode = std::atol(token.c_str());
            break;
        case 5: // 返回信息
            record.respMessage = token;
            break;
        case 6: // 后续包标示
            record.hasNextPacket = (token == "1");
            break;
        case 7: // 应答字段数（可能需要根据这个来解析后续数据）
            record.fieldCount = token;
            break;
        case 8: // 应答记录数（同上）
            record.recordCount = token;
            break;
        case 9: // 原请求功能号
            record.origFuncCode = token;
            break;
        case 10: // 请求包序列号
            record.reqPacketSeq = token;
            break;
            // 忽略保留字段和其他可能的字段...
        default:
            if (fieldIndex > 10) {
                record.reservedField.push_back(token);
            }
            break;
        }
    }

    return true;
}

//---------------------------------------------------------------------------
//签入KDGateway
bool MidGateway::signSystem()
{
    m_dwTotalDataLen = 0;

    //发送签入包
    if (sendMessage("100") != true)
        return false;

    tagAnswerPacket AnswerPacket;
    if (recvtagMessage(&AnswerPacket, 0) != true) //0表示不校验CRC
        return false;

    if (atol(AnswerPacket.szRetCode) != 0) {
        MFLOG_ERROR("ErrCode:{} ErrMsg:{}", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
        SetLastError(-8, "签入失败! ErrCode:%s ErrMsg:%s", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
        return false;
    }

    char szWorkKey[256];
    memset(szWorkKey, 0, 256);
    ats::GetValue(m_singleRecvBuffer + AnswerPacket.iHeadLen, szWorkKey, 2, '|', DEF_SBUFLEN);//记录数量  

    char szWorkKey_True[256];
    CBlowFish oBlowFish((unsigned char*)"SZKINGDOM", 9);
    oBlowFish.Decrypt((const unsigned char*)szWorkKey, (unsigned char*)szWorkKey_True, strlen(szWorkKey));

    strncpy(m_workKey, szWorkKey_True, 8);
    m_workKey[8] = 0;
    m_signsystem = true;

    return m_signsystem;
}

bool MidGateway::readySystem()
{
    if (!m_signsystem) {
        LOG4CXX_ERROR(logger, "尚未签入MID网关！");
        return false;
    }

	//系统状态
	if (sendMessage("90") != true)
		return false;
	//接收应答
	MidRespRecord respRecord;
    if (receiveMessage(respRecord) != true) {
		LOG4CXX_ERROR(logger, "柜台业务系统未就绪！");
		return false;
    }

	//网关状态
	if (sendMessage("97") != true)
		return false;
	if (receiveMessage(respRecord) != true)
		return false;

	//取系统时间
	if (sendMessage("98") != true)
		return false;
	if (receiveMessage(respRecord) != true)
		return false;

    return true;
}

//---------------------------------------------------------------------------
//发送请求
bool MidGateway::waitAnswer(const char* szRequest)
{
    m_dwTotalDataLen = 0;
    m_dwRecordNo = 0;

    int nct = 1;

    //发送请求
    if (sendMessage(szRequest) != true)
        return false;

    //接收应答
    tagAnswerPacket AnswerPacket;
    if (recvtagMessage(&AnswerPacket) != true) {
        MFLOG_DEBUG("RecvAnswer failed.");
        return false;
    }

    if (atol(AnswerPacket.szRetCode) != 0) {
        MFLOG_DETAIL("ErrCode:{} ErrMsg:{}", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
        SetLastError(-8, "ErrCode:%s ErrMsg:%s", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
        return false;
    }

    while (AnswerPacket.bIsNext) {
        //发送请求
        if (sendMessage("99") != true) //取后继包
            return false;

        //接收应答
        if (recvtagMessage(&AnswerPacket) != true) {
            MFLOG_DEBUG("RecvAnswer failed.");
            return false;
        }

        if (atol(AnswerPacket.szRetCode) != 0) {
            MFLOG_DETAIL("ErrCode:{} ErrMsg:{}", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
            SetLastError(-8, "ErrCode:%s ErrMsg:%s", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
            return false;
        }
    }
    return true;
}

bool MidGateway::rebuildMidFields(std::string& message)
{
	// 替换第3个字段-crc校验
	size_t pos = 0;
	for (size_t i = 0; i < 2; ++i) {
		pos = message.find('|', pos + 1);
	}
	size_t start = pos + 1;
	pos = message.find('|', start);
    size_t relen = pos - start;
	message.replace(start, relen, m_workKey);

    int64_t updlen = strlen(m_workKey) - relen;

	// 替换第5个字段-操作用户 
    if (strlen(m_workKey) == relen) {
		pos = message.find('|', pos + 1);
    }
    else if (strlen(m_workKey) < relen) {
		for (size_t i = 0; i < 2; ++i)
			pos = message.find('|', pos + 1);
    }
    else {
		pos = 0;
		for (size_t i = 0; i < 4; ++i)
			pos = message.find('|', pos + 1);
    }
	start = pos + 1;
	pos = message.find('|', start);
    relen = pos - start;
	message.replace(start, relen, m_opUser);

    updlen += strlen(m_opUser) - relen;
    int64_t headLen = std::atoi(message.substr(0, 4).c_str()) + updlen;
	// 替换请求头的长度字段
	std::stringstream headLenStream;
	headLenStream << std::setw(4) << std::setfill('0') << headLen;
    message.replace(0, 4, headLenStream.str());

	//设置CRC校验码
	char crcCode[DEF_SBUFLEN] = { 0 };
	CRC_Get((unsigned char*)message.c_str(), message.length(), (unsigned char*)crcCode);
	message.replace(10, 8, crcCode);

    return true;
}

bool MidGateway::rewriteMidFields(std::string& message)
{
	auto findNthField = [](const std::string& msg, size_t n) -> size_t {
		size_t pos = 0;
		for (size_t i = 0; i < n; ++i) {
			pos = msg.find('|', pos + 1);
			if (pos == std::string::npos) return std::string::npos;
		}
		return pos + 1;
	};

	size_t headLen, dataLen;
	extract(message.c_str(), 1, headLen);
	extract(message.c_str(), 2, dataLen);
	size_t msgLength = headLen + dataLen;

	// 替换第2个字段-数据长度
	if (msgLength != message.length() - headLen) {
		std::stringstream dataLenStream;
		dataLenStream << std::setw(4) << std::setfill('0') << message.length() - headLen;
        message.replace(5, 4, dataLenStream.str());
	}

	// 替换第3个字段-冗余校验
	size_t start = findNthField(message, 2);
	if (start == std::string::npos) return false;
	size_t end = message.find('|', start);
	size_t relen = end - start;
	message.replace(start, relen, m_workKey);

	// 设置CRC校验码
	char crcCode[DEF_SBUFLEN] = { 0 };
	CRC_Get((unsigned char*)message.c_str(), message.length(), (unsigned char*)crcCode);
	message.replace(10, 8, crcCode);

	return true;
}

bool MidGateway::refactorAllFields(std::string& message)
{
	size_t refpos = 0, count = 12;
	while (count-- && (refpos = message.find('|', refpos)) != std::string::npos)
		refpos++;

	// 构建请求包头
	std::stringstream midHeadStream;
    midHeadStream << "0000" << "|" // 包头大小
        << message.substr(5, 4) << "|" // 数据大小
        << m_workKey << "|" // CRC
        << m_szVersion << "|"
        << m_opUser << "|"
        << m_opSite << "|"
        << m_opBranch << "|"
        << m_opChannel << "|"
        << m_opSession << "|"
        << m_opRole << "|"
        << GetCurrentThreadId() << ":"
        << ++m_dwReqPakSN << "|"
        << m_opRouter << "|";

	// 替换请求头的长度字段
	std::stringstream headLenStream;
	std::string requestContent = midHeadStream.str();
	headLenStream << std::setw(4) << std::setfill('0') << requestContent.length();
	requestContent.replace(0, 4, headLenStream.str());

    requestContent.append(message.substr(refpos));

	//设置CRC校验码
	char szCRCCode[16] = { 0 };
	CRC_Get((unsigned char*)requestContent.data(), requestContent.length(), (unsigned char*)szCRCCode);
	requestContent.replace(10, 8, szCRCCode);

    message = requestContent;

    return true;
}

bool MidGateway::rebuildMatchQuery(std::string& request, std::string& user, std::string& stk)
{
	size_t refpos = 0, count = appConfig.funcfiled - 1;
	while (count-- && (refpos = request.find('|', refpos)) != std::string::npos)
		refpos++;

	std::string querytext = "1720|||";
	querytext.append(user).append("|||").append(stk).append("||||||||||||||||||Y|");

	// 替换数据头的长度字段
	std::stringstream data_len_stream;
	request = request.substr(0, refpos);
	data_len_stream << std::setw(4) << std::setfill('0') << querytext.length();
	request.replace(5, 4, data_len_stream.str());
	request.append(querytext);
    request.replace(10, 8, m_workKey);

	//设置CRC校验码
	char crc_code[16] = { 0 };
	CRC_Get((unsigned char*)request.data(), request.length(), (unsigned char*)crc_code);
	request.replace(10, 8, crc_code);

	return true;
}

bool MidGateway::waitQuery(std::string& request, std::string& user, std::string& stk, uint64_t num)
{
	size_t refpos = 0, count = appConfig.funcfiled - 3;
	while (count-- && (refpos = request.find('|', refpos)) != std::string::npos)
		refpos++;

	std::stringstream ss;
	ss << request.substr(0, refpos);
	ss << std::hex << GetCurrentThreadId() << ':';
	ss << std::hex << ++m_dwReqPakSN << '|';
	ss << "|505|||0|" << user << "|||||||||||0|300||||||||";

    // 构建委托查询请求
	std::string querystr = ss.str();
	rewriteMidFields(querystr);

	if (m_pSocket->sendBuffer(querystr.c_str(), (int)querystr.length()) <= 0) {
		// 重新登录
		if (!reConnect()) return false;
		m_pSocket->sendBuffer(querystr.c_str(), (int)querystr.length());
	}
	memset(m_responDataBuffer, 0, sizeof(m_responDataBuffer));

	//接收应答
	MidRespRecord respRecord;
	if (receiveMessage(respRecord) != true)
		return false;

	while ("1" == respRecord.hasNextPacket) {
		//发送请求
		if (sendMessage("99") != true) //取后继包
			return false;

		//接收应答
		if (receiveMessage(respRecord) != true)
			return false;

		if (respRecord.respCode != 0) {
			std::stringstream errorStream;
			errorStream << "waitAnswer code: " << respRecord.respCode << " detail: " << respRecord.respMessage;
			logErrorMessage(errorStream.str().c_str());
		}
	}

    return true;
}

bool MidGateway::waitMatch(std::string& request, std::string& user, std::string& stk, uint64_t num)
{
	if (m_pSocket == NULL)
		return false;

	static std::mutex mtx;
	static auto last_call_time = std::chrono::steady_clock::now() - std::chrono::seconds(1);

    // 线程安全，确保一秒钟只能被查询一次
	std::lock_guard<std::mutex> lock(mtx);
	auto now = std::chrono::steady_clock::now();
	auto duration_since_last_call = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_call_time).count();

    std::string inquiry_text = request;
    rebuildMatchQuery(inquiry_text, user, stk);

	m_pSocket->clearBuffer();
	if (m_pSocket->sendBuffer(inquiry_text.c_str(), (int)inquiry_text.length()) <= 0)
		if (reConnect()) m_pSocket->sendBuffer(inquiry_text.c_str(), (int)inquiry_text.length());
	memset(m_responDataBuffer, 0, sizeof(m_responDataBuffer));

	//接收应答
	MidRespRecord respRecord;
	if (receiveMessage(respRecord) != true)
		return false;

	while ("1" == respRecord.hasNextPacket) {
		//发送请求
		if (sendMessage("99") != true) //取后继包
			return false;

		//接收应答
		if (receiveMessage(respRecord) != true)
			return false;

		if (respRecord.respCode != 0) {
			std::stringstream errorStream;
			errorStream << "waitAnswer code: " << respRecord.respCode << " detail: " << respRecord.respMessage;
			logErrorMessage(errorStream.str().c_str());
		}
	}

    // 未到一秒钟，线程停下来等待一秒剩余
	if (duration_since_last_call < 1000) {
		std::this_thread::sleep_for(std::chrono::milliseconds(1000 - duration_since_last_call));
	}
	last_call_time = std::chrono::steady_clock::now();

    return true;
}

bool MidGateway::waitAnswer(std::string& request, std::string& anser, uint64_t num)
{
    rewriteMidFields(request);
	if (m_pSocket == NULL)
		return false;
    else
        m_pSocket->clearBuffer();

    // 记录发送前的时间
    int repeat_count = 0;
    auto start = std::chrono::high_resolution_clock::now();

    std::string seqSession;
    extract(request.c_str(), 11, seqSession);

send_repeat:
    if (m_pSocket->sendBuffer(request.c_str(), (int)request.length()) <= 0) {
		// 重新登录并且发送
        if (!reConnect()) {
			LOG4CXX_ERROR(logger, "Line [" << num << "] <waitAnswer::reConnect> MID网关重连失败！");
            return false;
        }
        if (m_pSocket->sendBuffer(request.c_str(), (int)request.length()) <= 0) {
			LOG4CXX_ERROR(logger, "Line [" << num << "] <waitAnswer::sendBuffe> 请求重新发送失败！");
			return false;
        }
		LOG4CXX_INFO(logger, "Line [" << num << "] <waitAnswer::sendBuffe> 请求重新发送成功！");
    }
	memset(m_responDataBuffer, 0, sizeof(m_responDataBuffer));

	//接收应答
    MidRespRecord respRecord;
    bool rcvresult = receiveMessage(seqSession, respRecord, num);
	auto end = std::chrono::high_resolution_clock::now();
	auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();

	if (rcvresult != true) {
		std::stringstream errorStream;
		errorStream << "Line [" << num << "] code[" << respRecord.respCode << "]detail: receiveMessage failed, duration: " << duration << "ms";
        LOG4CXX_ERROR(logger, "Line [" << num << "] execute error:" << GetLastErrorMessage() << " request:" << request);
        if (!reConnect()) {
            LOG4CXX_ERROR(logger, "Line [" << num << "] code[" << respRecord.respCode << "] MID网关重连失败！");
        }
		// 存储数据
		std::lock_guard<std::mutex> guard(g_dataMutex);
		g_anserData[num] = errorStream.str();
		return false;
	}

	MidRespRecord origRecord;
	if (!anser.empty()) parseResponse(anser.c_str(), anser.length(), origRecord);
	if (origRecord.respCode != respRecord.respCode)
	{
		if (415 == respRecord.respCode && ++repeat_count < 6) {
			LOG4CXX_ERROR(logger, "Line[" << num << ":" << repeat_count << "] code[" << respRecord.respCode << "]detail: " << respRecord.respMessage << " request: " << request);
			if (!reConnect()) {
				LOG4CXX_ERROR(logger, "waitAnswer::reConnect失败！");
				return false;
			}
			goto send_repeat;
		}
		std::stringstream errorStream;
		errorStream << "Line [" << num << "] code[" << respRecord.respCode << "]detail: " << respRecord.respMessage << ", duration: " << duration << "ms";
		// 存储数据
		std::lock_guard<std::mutex> guard(g_dataMutex);
		g_anserData[num] = errorStream.str();
	}

	if (respRecord.respCode != 0) {
		return false;
	}
    else if (repeat_count) {
		LOG4CXX_INFO(logger, "waitAnswer::resendBuffer成功！");
    }

	while ("1" == respRecord.hasNextPacket) {
		//发送请求
		if (sendMessage("99") != true) //取后继包
			return false;

		//接收应答
		if (receiveMessage(respRecord) != true)
			return false;

        if (respRecord.respCode != 0) {
			std::stringstream errorStream;
			errorStream << "waitAnswer code: " << respRecord.respCode << " detail: " << respRecord.respMessage;
            logErrorMessage(errorStream.str().c_str());
		}
	}

    return true;
}

bool MidGateway::waitAnswerOpt(std::vector<std::string> vecData, std::function<void()> sync_func)
{
    m_dwTotalDataLen = 0;
    m_dwRecordNo = 0;

    int nct = 1;

    //发送请求
    if (sendRequestOpt(vecData) != true)
        return false;

    if (sync_func) {
        sync_func();
    }

    //接收应答
    tagAnswerPacket AnswerPacket;
    if (recvtagMessage(&AnswerPacket) != true) {
        MFLOG_DEBUG("RecvAnswer failed.");
        return false;
    }

    if (atol(AnswerPacket.szRetCode) != 0) {
        MFLOG_DETAIL("ErrCode:{} ErrMsg:{}", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
        SetLastError(-8, "ErrCode:%s ErrMsg:%s", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
        return false;
    }

    //printf("\nrecvbuf=[%d][%s]",nct++,m_pSingleRecvBuffer);

    while (AnswerPacket.bIsNext) {
        //发送请求
        if (sendMessage("99") != true) //取后继包
            return false;

        //接收应答
        if (recvtagMessage(&AnswerPacket) != true) {
            MFLOG_DEBUG("RecvAnswer failed.");
            return false;
        }

        //printf("\nrecvbuf=[%d][%s]",nct++,m_pSingleRecvBuffer);

        if (atol(AnswerPacket.szRetCode) != 0) {
            MFLOG_DETAIL("ErrCode:{} ErrMsg:{}", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
            SetLastError(-8, "ErrCode:%s ErrMsg:%s", AnswerPacket.szRetCode, AnswerPacket.szRetMsg);
            return false;
        }
    }
    return true;
}

void MidGateway::setParams(const char* param)
{
	std::string value;
	extract(param, 5, value);
	setOpUser(value.c_str());

	extract(param, 6, value);
    setOpSite(value.c_str());

	extract(param, 7, value);
    setOpBranch(value.c_str());

	extract(param, 8, value);
    setOpChannel(value.c_str());

	extract(param, 10, value);
    setOpRouter(value.c_str());
}

//---------------------------------------------------------------------------
//登录
bool MidGateway::loginSystem(char userType, char* userID, char* szPasswd)
{
    char szPassword[200];

    memset(szPassword, 0, 200);

    CBlowFish oBlowFish((unsigned char*)m_workKey, strlen(m_workKey));
    oBlowFish.Encrypt((unsigned char*)szPasswd, (unsigned char*)szPassword, strlen(szPasswd));

    //登录
    char szTemp[512];
    sprintf(szTemp, "301|%c|%s|%s|", userType, userID, szPassword);

    char pTemp[32] = { 0 };
    strcpy(pTemp, "1");
    setOpRole(pTemp);
    if (waitAnswer(szTemp) != true) {
        strcpy(pTemp, "2");
        setOpRole(pTemp);
        return false;
    }
    strcpy(pTemp, "2");
    setOpRole(pTemp);


    return true;
}

void MidGateway::setTrace(bool btrace)
{
    m_btraceon = btrace;
}

bool MidGateway::extract(const char* data, int field, size_t& value)
{
    std::string exval;
    if (!extract(data, field, exval))
        return false;

    value = std::atol(exval.c_str());

    return true;
}

bool MidGateway::extract(const char* data, int field, std::string& value)
{
	int fieldIndex = 0;
	const char* current = data;

    if (field < 2) {
        value.assign(current, 4);
        return true;
    }

	// 查找用户号（第N个字段）
	while (fieldIndex < field && *current) {
		if (*current == '|') {
			++fieldIndex;
			if (fieldIndex == field - 1) {
				++current;
				value.assign(current, strcspn(current, "|"));
				break;
			}
		}
		++current;
	}

	return fieldIndex == field - 1;
}

// 从缓冲区获得第N行数据，row_num 从 0 开始
bool MidGateway::getRow(const char* src, char* dest, int filed_num, int row_num, int data_len, char ch)
{
    int start_pos = 0;
    int end_pos = 0;

    int start_num = 1; // 从第N个‘|’开始，从0开始
    int end_num = 1;   // 到N个‘|’结束，从0开始
    int cur_num = 0;

    start_num = row_num * filed_num; // 从第N个‘|’开始，从1开始
    end_num = (row_num + 1) * filed_num; // 到N个‘|’结束，从1开始

    // 1 定位start_pos
    while (cur_num < start_num && start_pos < data_len) {
        if (*(src + start_pos) == ch) {
            ++cur_num;
        }
        ++start_pos;
    }

    // 2 定位end_pos
    end_pos = start_pos + 1;
    while (cur_num < end_num && end_pos < data_len) {
        if (*(src + end_pos) == ch) {
            ++cur_num;
        }
        ++end_pos;
    }

    // 3 截取
    if (end_pos > start_pos && end_pos > 0) {
        memcpy(dest, src + start_pos, end_pos - start_pos);
    }
    else {
        SetLastError(-8, "ErrCode:%d ErrMsg:%s", -8, "GetRow parse data error!");
        return false;
    }

    return true;
}

const char* MidGateway::getDataHead()
{
    memset(m_szReturnHead, 0x00, sizeof(m_szReturnHead));

    // 解析应答包头，获取字段数
    std::vector<std::string> vecHead;
    if (m_responDataBuffer[0] > 0) {
        std::string strParam = m_responDataBuffer;
        vecHead = splitString(strParam, "|");
    }
    else {
        SetLastError(-1, "ErrCode:%d ErrMsg:%s", -1, "GetDataHead parse data error1!");
        return NULL;
    }

    if (vecHead.size() == 12 and m_dwTotalDataLen > 0) {
        int field_num = atoi(vecHead[7].c_str());
        if (getRow(m_returnDataBuffer, m_szReturnHead, field_num, 0, m_dwTotalDataLen, '|') != true) {
            SetLastError(-2, "ErrCode:%d ErrMsg:%s", -2, "GetDataHead parse data error3!");
            return NULL;
        }
    }
    else {
        SetLastError(-3, "ErrCode:%d ErrMsg:%s", -3, "GetDataHead parse data error3!");
        return NULL;
    }
    return m_szReturnHead;
}

bool MidGateway::getDataRows(std::vector<std::string>& data_rows)
{
    data_rows.clear();

    // 解析应答包头，获取字段数
    std::vector<std::string> vecHead;
    if (m_responDataBuffer[0] > 0) {
        std::string strParam = m_responDataBuffer;
        vecHead = splitString(strParam, "|");
    }
    else {
        SetLastError(-1, "ErrCode:%d ErrMsg:%s", -1, "GetDataRows parse data error1!");
        return false;
    }

    if (vecHead.size() == 12 and m_dwTotalDataLen > 0) {
        int field_num = atoi(vecHead[7].c_str());

        char szDataRow[4096];

        for (int i = 0; i < (int)m_dwRecordNo; ++i) {
            memset(szDataRow, 0, 4096);
            if (getRow(m_returnDataBuffer, szDataRow, field_num, i + 1, m_dwTotalDataLen, '|') != true) {
                SetLastError(-2, "ErrCode:%d ErrMsg:%s", -2, "GetDataRows parse data error3!");
                return false;
            }
            data_rows.push_back(szDataRow);
        }
    }
    else {
        SetLastError(-3, "ErrCode:%d ErrMsg:%s", -3, "GetDataRows parse data error3!");
        return false;
    }


    return true;
}

bool MidGateway::getDataRowsOpt(std::vector<std::string>& data_rows)
{
    data_rows.clear();

    // 解析应答包头，获取字段数
    std::vector<std::string> vecHead;
    if (m_responDataBuffer[0] > 0) {
        std::string strParam = m_responDataBuffer;
        vecHead = splitString(strParam, "|");
    }
    else {
        SetLastError(-1, "ErrCode:%d ErrMsg:%s", -1, "GetDataRowsOpt parse data error1!");
        return false;
    }

    if (vecHead.size() == 12 and m_dwTotalDataLen > 0) {
        int field_num = atoi(vecHead[7].c_str());

        // head
        char szHead[1024] = { 0 };
        if (getRow(m_returnDataBuffer, szHead, field_num, 0, m_dwTotalDataLen, '|') != true) {
            SetLastError(-2, "ErrCode:%d ErrMsg:%s", -2, "GetDataRowsOpt parse data error3!");
            return false;
        }
        std::vector<std::string>  data_head = splitString(szHead, "|");
        if (data_head.size() != field_num) {
            SetLastError(-3, "ErrCode:%d ErrMsg:%s", -3, "GetDataRowsOpt parse data error3!");
            return false;
        }

        // data
        char szDataRow[4096];
        for (int i = 0; i < (int)m_dwRecordNo; ++i) {
            memset(szDataRow, 0, 4096);
            if (getRow(m_returnDataBuffer, szDataRow, field_num, i + 1, m_dwTotalDataLen, '|') != true) {
                SetLastError(-4, "ErrCode:%d ErrMsg:%s", -4, "GetDataRowsOpt parse data error3!");
                return false;
            }

            // 转变成key - value格式
            std::vector<std::string>  data_single = splitString(szDataRow, "|");
            if (data_single.size() != field_num) {
                SetLastError(-5, "ErrCode:%d ErrMsg:%s", -5, "GetDataRowsOpt parse data error3!");
                return false;
            }

            std::string strTemp;
            for (int i = 0; i < field_num; ++i) {
                if (i == 0) {
                    strTemp = data_head[i] + ":" + data_single[i];
                }
                else {
                    strTemp = strTemp + "," + data_head[i] + ":" + data_single[i];
                }
            }

            data_rows.push_back(strTemp);
        }

    }
    else {
        SetLastError(-9, "ErrCode:%d ErrMsg:%s", -9, "GetDataRowsOpt parse data error3!");
        return false;
    }

    return true;
}

void MidGateway::logErrorMessage(int errcode, const char* errInfo, ...)
{
	va_list arg_ptr;
	va_start(arg_ptr, errInfo);
	char fmtbuffer[1024] = { 0 };
	_vsnprintf(fmtbuffer, sizeof(fmtbuffer) - 1, errInfo, arg_ptr);
	va_end(arg_ptr);

    logErrorMessage(fmtbuffer, errcode);
}

void MidGateway::logErrorMessage(const char* errInfo, int errCode, bool sysError)
{
	if (!errInfo || !*errInfo) return;

    if (sysError)
    {
		LPSTR buffer = nullptr;
		DWORD dwCode = ::GetLastError();
		auto status = ::FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
			nullptr, dwCode, 0, (LPSTR)&buffer, 0, nullptr);

		if (status != 0 && buffer != nullptr)
		{
			SetLastError(errCode, buffer);
			//LOG4CXX_ERROR(logger, errInfo << "：" << buffer);
			LocalFree(buffer);
		}
		else
		{
			LOG4CXX_ERROR(logger, "Failed to retrieve error message.");
		}
    }
    else
    {
		SetLastError(errCode, errInfo);
		//LOG4CXX_ERROR(logger, errInfo);
    }
}

std::vector<std::string> MidGateway::splitStream(const std::string& str, char delimiter)
{
	std::string token;
	std::vector<std::string> tokens;

	std::istringstream tokenStream(str);
	while (std::getline(tokenStream, token, delimiter)) {
		tokens.push_back(token);
	}

	return tokens;
}

std::vector<std::string> MidGateway::splitStream(const char* str, int len, char delimiter)
{
	std::string token;
	std::vector<std::string> tokens;

	std::istringstream tokenStream(str, len);
	while (std::getline(tokenStream, token, delimiter)) {
		tokens.push_back(token);
	}

	return tokens;
}

std::vector<std::string> MidGateway::splitString(const std::string& str, const std::string& pattern)
{
    std::vector<std::string> resVec;

    if ("" == str) {
        return resVec;
    }
    //方便截取最后一段数据，最后有|,此处不需要补分隔符
    // std::string strs = str + pattern;
    std::string strs = str;

    size_t pos = strs.find(pattern);
    size_t size = strs.size();

    while (pos != std::string::npos) {
        std::string x = strs.substr(0, pos);
        resVec.push_back(x);
        strs = strs.substr(pos + 1, size);
        pos = strs.find(pattern);
    }

    return resVec;
}