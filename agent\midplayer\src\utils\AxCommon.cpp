﻿#include "stdafx.h"
#include <signal.h>
#include <string.h>
#include <cmath>


// ==================== AxCommon实现 ====================

static char* ax_strptime(const char* buf, const char* fmt, struct tm* tm)
{
	char	c;
	const char* ptr;
	int	i, len;
	int Ealternative, Oalternative;
	char isLeapYear;
	int mon, year;

	ptr = fmt;
	while (*ptr != 0) {
		if (*buf == 0)
			break;

		c = *ptr++;

		if (c != '%') {
			if (isspace((unsigned char)c))
				while (*buf != 0 && isspace((unsigned char)*buf))
					buf++;
			else if (c != *buf++)
				return 0;
			continue;
		}

		Ealternative = 0;
		Oalternative = 0;
		c = *ptr++;
		switch (c) {
		case 0:
		case '%':
			if (*buf++ != '%')
				return 0;
			break;

		case 'M':
		case 'S':
			if (*buf == 0 || isspace((unsigned char)*buf))
				break;

			if (!isdigit((unsigned char)*buf))
				return 0;

			len = 2;
			for (i = 0; len && *buf != 0 && isdigit((unsigned char)*buf); buf++) {
				i *= 10;
				i += *buf - '0';
				len--;
			}

			if (c == 'M') {
				if (i > 59)
					return 0;
				tm->tm_min = i;
			}
			else {
				if (i > 60)
					return 0;
				tm->tm_sec = i;
			}

			if (*buf != 0 && isspace((unsigned char)*buf))
				while (*ptr != 0 && !isspace((unsigned char)*ptr))
					ptr++;
			break;

		case 'H':
		case 'I':
		case 'k':
		case 'l':
			if (!isdigit((unsigned char)*buf))
				return 0;

			len = 2;
			for (i = 0; len && *buf != 0 && isdigit((unsigned char)*buf); buf++) {
				i *= 10;
				i += *buf - '0';
				len--;
			}
			if (c == 'H' || c == 'k') {
				if (i > 23)
					return 0;
			}
			else if (i > 12)
				return 0;

			tm->tm_hour = i;

			if (*buf != 0 && isspace((unsigned char)*buf))
				while (*ptr != 0 && !isspace((unsigned char)*ptr))
					ptr++;
			break;

		case 'd':
		case 'e':
			if (!isdigit((unsigned char)*buf))
				return 0;

			len = 2;
			for (i = 0; len && *buf != 0 && isdigit((unsigned char)*buf); buf++) {
				i *= 10;
				i += *buf - '0';
				len--;
			}
			if (i > 31)
				return 0;

			tm->tm_mday = i;

			if (*buf != 0 && isspace((unsigned char)*buf))
				while (*ptr != 0 && !isspace((unsigned char)*ptr))
					ptr++;
			break;

		case 'm':
			if (!isdigit((unsigned char)*buf))
				return 0;

			len = 2;
			for (i = 0; len && *buf != 0 && isdigit((unsigned char)*buf); buf++) {
				i *= 10;
				i += *buf - '0';
				len--;
			}
			if (i < 1 || i > 12)
				return 0;

			tm->tm_mon = i - 1;

			if (*buf != 0 && isspace((unsigned char)*buf))
				while (*ptr != 0 && !isspace((unsigned char)*ptr))
					ptr++;
			break;

		case 'Y':
		case 'y':
			if (*buf == 0 || isspace((unsigned char)*buf))
				break;

			if (!isdigit((unsigned char)*buf))
				return 0;

			len = (c == 'Y') ? 4 : 2;
			for (i = 0; len && *buf != 0 && isdigit((unsigned char)*buf); buf++) {
				i *= 10;
				i += *buf - '0';
				len--;
			}
			if (c == 'Y')
				i -= 1900;
			if (c == 'y' && i < 69)
				i += 100;
			if (i < 0)
				return 0;

			tm->tm_year = i;

			if (*buf != 0 && isspace((unsigned char)*buf))
				while (*ptr != 0 && !isspace((unsigned char)*ptr))
					ptr++;
			break;
		}
	} // Fix up yday field
	year = tm->tm_year + 1900;
	isLeapYear = (year % 4 == 0) - (year % 100 == 0) + (year % 400 == 0) - (year % 4000 == 0);
	mon = tm->tm_mon; // 0 == January
	tm->tm_yday =
		(mon > 0) * 31 + // Jan
		(mon > 1) * (28 + isLeapYear) + // Feb
		(mon > 2) * 31 + // March
		(mon > 3) * 30 + // April
		(mon > 4) * 31 + // May
		(mon > 5) * 30 + // June
		(mon > 6) * 31 + // July
		(mon > 7) * 31 + // Aug
		(mon > 8) * 30 + // Sept
		(mon > 9) * 31 + // Oct
		(mon > 10) * 30 + // Nov
		+tm->tm_mday - 1;

	return (char*)buf;
}

bool AxCommon::GetBit(uint32_t src, uint32_t pos)
{
	bool retval = false;
	if (pos <= sizeof(uint32_t) * 8 - 1) {
		uint32_t nMask = 1 << pos;
		retval = (src & nMask) == nMask;
	};
	return retval;
}

void AxCommon::SetBit(uint32_t& src, uint32_t pos, bool flag)
{
	if (pos <= sizeof(uint32_t) * 8 - 1) {
		if (flag) {
			src |= (1 << pos);
		}
		else {
			src &= ~(1 << pos);
		}
	};
}

std::string AxCommon::trim(const std::string& sStr, const std::string& s, bool bChar)
{
	if (sStr.empty())
	{
		return sStr;
	}

	/**
	* 将完全与s相同的字符串去掉
	*/
	if (!bChar)
	{
		return trimright(trimleft(sStr, s, false), s, false);
	}

	return trimright(trimleft(sStr, s, true), s, true);
}

std::string AxCommon::trimleft(const std::string& sStr, const std::string& s, bool bChar)
{
	if (sStr.empty())
	{
		return sStr;
	}

	/**
	* 去掉sStr左边的字符串s
	*/
	if (!bChar)
	{
		if (sStr.length() < s.length())
		{
			return sStr;
		}

		if (sStr.compare(0, s.length(), s) == 0)
		{
			return sStr.substr(s.length());
		}

		return sStr;
	}

	/**
	* 去掉sStr左边的 字符串s中的字符
	*/
	std::string::size_type pos = 0;
	while (pos < sStr.length())
	{
		if (s.find_first_of(sStr[pos]) == std::string::npos)
		{
			break;
		}

		pos++;
	}

	if (pos == 0) return sStr;

	return sStr.substr(pos);
}

std::string AxCommon::trimright(const std::string& sStr, const std::string& s, bool bChar)
{
	if (sStr.empty())
	{
		return sStr;
	}

	/**
	* 去掉sStr右边的字符串s
	*/
	if (!bChar)
	{
		if (sStr.length() < s.length())
		{
			return sStr;
		}

		if (sStr.compare(sStr.length() - s.length(), s.length(), s) == 0)
		{
			return sStr.substr(0, sStr.length() - s.length());
		}

		return sStr;
	}

	/**
	* 去掉sStr右边的 字符串s中的字符
	*/
	std::string::size_type pos = sStr.length();
	while (pos != 0)
	{
		if (s.find_first_of(sStr[pos - 1]) == std::string::npos)
		{
			break;
		}

		pos--;
	}

	if (pos == sStr.length()) return sStr;

	return sStr.substr(0, pos);
}

std::string AxCommon::lower(const std::string& s)
{
	std::string sString = s;
	for (std::string::iterator iter = sString.begin(); iter != sString.end(); ++iter)
	{
		*iter = tolower(*iter);
	}

	return sString;
}

std::string AxCommon::upper(const std::string& s)
{
	std::string sString = s;

	for (std::string::iterator iter = sString.begin(); iter != sString.end(); ++iter)
	{
		*iter = toupper(*iter);
	}

	return sString;
}

bool AxCommon::isdigit(const std::string& sInput)
{
	std::string::const_iterator iter = sInput.begin();

	if (sInput.empty())
	{
		return false;
	}

	while (iter != sInput.end())
	{
		if (!::isdigit(*iter))
		{
			return false;
		}
		++iter;
	}
	return true;
}

int AxCommon::str2tm(const std::string& sString, const std::string& sFormat, struct tm& stTm)
{
	char* p = ax_strptime(sString.c_str(), sFormat.c_str(), &stTm);
	return (p != NULL) ? 0 : -1;
}

int AxCommon::strgmt2tm(const std::string& sString, struct tm& stTm)
{
	return str2tm(sString, "%a, %d %b %Y %H:%M:%S GMT", stTm);
}

std::string AxCommon::tm2str(const struct tm& stTm, const std::string& sFormat)
{
	char sTimeString[255] = "\0";

	strftime(sTimeString, sizeof(sTimeString), sFormat.c_str(), &stTm);

	return std::string(sTimeString);
}

std::string AxCommon::tm2str(const time_t& t, const std::string& sFormat)
{
	// 将时间转换为当地时间
	struct tm tt;
	localtime_r(&t, &tt);

	return tm2str(tt, sFormat);
}

std::string AxCommon::now2str(const std::string& sFormat)
{
	time_t t = time(NULL);
	return tm2str(t, sFormat.c_str());
}

std::string AxCommon::now2GMTstr()
{
	time_t t = time(NULL);
	return tm2GMTstr(t);
}

std::string AxCommon::tm2GMTstr(const time_t& t)
{
#if AX_PLATFORM == AX_PLATFORM_LINUX
	struct tm tt;
	gmtime_r(&t, &tt);
	return tm2str(tt, "%a, %d %b %Y %H:%M:%S GMT");
#else
	return std::string();
#endif
}

std::string AxCommon::tm2GMTstr(const struct tm& stTm)
{
	return tm2str(stTm, "%a, %d %b %Y %H:%M:%S GMT");
}

std::string AxCommon::nowdate2str()
{
	return now2str("%Y%m%d");
}

std::string AxCommon::nowtime2str()
{
	return now2str("%H%M%S");
}

int64_t AxCommon::now2ms()
{
	return AXGetTime();
}

int64_t AxCommon::now2us()
{
	return AXGetMicroSecondTime();
}

//参照phorix的优化
static char c_b2s[256][4] = { "00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff" };

std::string AxCommon::bin2str(const void* buf, size_t len, const std::string& sSep, size_t lines)
{
	if (buf == NULL || len <= 0)
	{
		return "";
	}

	std::string sOut;
	const unsigned char* p = (const unsigned char*)buf;

	for (size_t i = 0; i < len; ++i, ++p)
	{
		sOut += c_b2s[*p][0];
		sOut += c_b2s[*p][1];
		sOut += sSep;

		//换行
		if ((lines != 0) && ((i + 1) % lines == 0))
		{
			sOut += "\n";
		}
	}

	return sOut;
}

std::string AxCommon::bin2str(const std::string& sBinData, const std::string& sSep, size_t lines)
{
	return bin2str((const void*)sBinData.data(), sBinData.length(), sSep, lines);
}

int AxCommon::str2bin(const char* psAsciiData, unsigned char* sBinData, int iBinSize)
{
	int iAsciiLength = strlen(psAsciiData);

	int iRealLength = (iAsciiLength / 2 > iBinSize) ? iBinSize : (iAsciiLength / 2);
	for (int i = 0; i < iRealLength; i++)
	{
		sBinData[i] = x2c(psAsciiData + i * 2);
	}
	return iRealLength;
}

std::string AxCommon::str2bin(const std::string& sString, const std::string& sSep, size_t lines)
{
	const char* psAsciiData = sString.c_str();

	int iAsciiLength = sString.length();
	std::string sBinData;
	for (int i = 0; i < iAsciiLength; i++)
	{
		sBinData += x2c(psAsciiData + i);
		i++;
		i += sSep.length(); //过滤掉分隔符

		if (lines != 0 && sBinData.length() % lines == 0)
		{
			i++;    //过滤掉回车
		}
	}

	return sBinData;
}

char AxCommon::x2c(const std::string& sWhat)
{
	register char digit;

	if (sWhat.length() < 2)
	{
		return '\0';
	}

	digit = (sWhat[0] >= 'A' ? ((sWhat[0] & 0xdf) - 'A') + 10 : (sWhat[0] - '0'));
	digit *= 16;
	digit += (sWhat[1] >= 'A' ? ((sWhat[1] & 0xdf) - 'A') + 10 : (sWhat[1] - '0'));

	return(digit);
}

std::string AxCommon::replace(const std::string& sString, const std::string& sSrc, const std::string& sDest)
{
	if (sSrc.empty())
	{
		return sString;
	}

	std::string sBuf = sString;

	std::string::size_type pos = 0;

	while ((pos = sBuf.find(sSrc, pos)) != std::string::npos)
	{
		sBuf.replace(pos, sSrc.length(), sDest);
		pos += sDest.length();
	}

	return sBuf;
}

std::string AxCommon::replace(const std::string& sString, const std::map<std::string, std::string>& mSrcDest)
{
	if (sString.empty())
	{
		return sString;
	}

	std::string tmp = sString;
	std::map<std::string, std::string>::const_iterator it = mSrcDest.begin();

	while (it != mSrcDest.end())
	{

		std::string::size_type pos = 0;
		while ((pos = tmp.find(it->first, pos)) != std::string::npos)
		{
			tmp.replace(pos, it->first.length(), it->second);
			pos += it->second.length();
		}

		++it;
	}

	return tmp;
}

bool AxCommon::matchPeriod(const std::string& s, const std::string& pat)
{
	if (s.empty())
	{
		return false;
	}

	if (pat.empty())
	{
		return true;
	}

	if (pat.find('*') == std::string::npos)
	{
		return s == pat;
	}

	std::string::size_type sIndex = 0;
	std::string::size_type patIndex = 0;
	do
	{
		if (pat[patIndex] == '*')
		{
			if (s[sIndex] == '.')
			{
				return false;
			}

			while (sIndex < s.size() && s[sIndex] != '.')
			{
				++sIndex;
			}
			patIndex++;
		}
		else
		{
			if (pat[patIndex] != s[sIndex])
			{
				return false;
			}
			++sIndex;
			++patIndex;
		}
	} while (sIndex < s.size() && patIndex < pat.size());

	return sIndex == s.size() && patIndex == pat.size();
}

bool AxCommon::matchPeriod(const std::string& s, const std::vector<std::string>& pat)
{
	for (size_t i = 0; i < pat.size(); i++)
	{
		if (AxCommon::matchPeriod(s, pat[i]))
		{
			return true;
		}
	}
	return false;
}

bool AxCommon::isPrimeNumber(size_t n)
{
	size_t nn = (size_t)sqrt((double)n);
	for (size_t i = 2; i < nn; i++)
	{
		if (n % i == 0)
		{
			return false;
		}
	}
	return true;
}

size_t AxCommon::toSize(const std::string& s, size_t iDefaultSize)
{
	if (s.empty())
	{
		return iDefaultSize;
	}

	char c = s[s.length() - 1];
	if (c != 'K' && c != 'M' && c != 'G' && AxCommon::trim(s) == AxCommon::tostr(AxCommon::strto<size_t>(s)))
	{
		//没有后缀, 且转换是正确的
		return (size_t)(AxCommon::strto<size_t>(s));
	}
	else if (c == 'K' || c == 'M' || c == 'G')
	{
		if (s.length() == 1)
		{
			return iDefaultSize;
		}

		float n = AxCommon::strto<float>(s.substr(0, s.length() - 1));

		if (AxCommon::trim(s.substr(0, s.length() - 1)) != AxCommon::tostr<float>(n))
		{
			return iDefaultSize;
		}

		if (c == 'K')
		{
			return (size_t)(n * 1024);
		}
		if (c == 'M')
		{
			return (size_t)(n * 1024 * 1024);
		}
		if (c == 'G') //-V547
		{
			return (size_t)(n * 1024 * 1024 * 1024);
		}
	}

	return iDefaultSize;
}

AXIdGenerator::AXIdGenerator(uint64_t server_id)
{
	m_nServerID = server_id;
	m_nLastTime = time(NULL);
	m_nLastID = (m_nServerID << 52) + (m_nLastTime << 20) + 0;
}

uint64_t AXIdGenerator::GenId()
{
	uint64_t nCurrTime = time(NULL);
	if (nCurrTime == m_nLastTime)
	{
		return ++m_nLastID;
	}

	m_nLastTime = nCurrTime;
	m_nLastID = (m_nServerID << 52) + (m_nLastTime << 20) + 0;
	return m_nLastID;
}

bool AxCommon::IsValidPhone(const std::string& phone)
{
	return true;
}

bool AxCommon::ExtractFileFromExecutable(int resfileNumber, const char* savfileName)
{
	// 定位我们的自定义资源
	HMODULE hModule = GetModuleHandle(NULL);
	if (hModule == NULL)
	{
		std::cerr << "错误：获取模块句柄失败。" << std::endl;
		return false;
	}

	HRSRC hRsrc = FindResource(hModule, MAKEINTRESOURCE(resfileNumber), TEXT("CONFIG"));
	if (hRsrc == NULL)
	{
		std::cerr << "错误：无法找到资源。" << std::endl;
		return false;
	}

	// 获取资源大小
	DWORD dwSize = SizeofResource(hModule, hRsrc);
	if (dwSize == 0)
	{
		std::cerr << "错误：无效的资源大小。" << std::endl;
		return false;
	}

	// 加载资源
	HGLOBAL hGlobal = LoadResource(hModule, hRsrc);
	if (hGlobal == NULL)
	{
		std::cerr << "错误：无法加载资源。" << std::endl;
		return false;
	}

	// 锁定资源
	LPVOID lpVoid = LockResource(hGlobal);
	if (lpVoid == NULL)
	{
		std::cerr << "错误：无法锁定资源。" << std::endl;
		FreeResource(hGlobal);  // 在返回前释放资源
		return false;
	}

	// 将资源写入文件
	FILE* fp = fopen(savfileName, "wb+");
	if (fp == NULL)
	{
		std::cerr << "错误：无法创建或打开文件。" << std::endl;
		FreeResource(hGlobal);
		return false;
	}

	fwrite(lpVoid, sizeof(char), dwSize, fp);
	fclose(fp);

	// 释放资源
	FreeResource(hGlobal);

	return true;
}