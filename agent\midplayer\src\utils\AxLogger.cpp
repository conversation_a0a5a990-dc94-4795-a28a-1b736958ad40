﻿#include "stdafx.h"
#include "AxLogger.h"
#include <filesystem>
#include <iostream>

MidPlayerLogger& MidPlayerLogger::getInstance() {
	static MidPlayerLogger instance;
	return instance;
}

MidPlayerLogger::~MidPlayerLogger() {
	shutdown();
}

void MidPlayerLogger::initialize() {
	if (initialized_.load()) {
		return;
	}

	try {
		// 使用配置系统获取日志配置
		auto& config = MidPlayerConfig::getInstance();
		auto loggingConfig = config.getLoggingConfig();
		initialize(loggingConfig);
	}
	catch (const std::exception& e) {
		// 如果配置系统失败，使用默认配置
		MidPlayerConfig::LoggingConfig defaultConfig;
		initialize(defaultConfig);
		std::cerr << "使用默认日志配置，配置系统错误: " << e.what() << std::endl;
	}
}

void MidPlayerLogger::initialize(const MidPlayerConfig::LoggingConfig& config) {
	if (initialized_.load()) {
		return;
	}

	try {
		createLogger(config);

		initialized_.store(true);
	}
	catch (const std::exception& e) {
		std::cerr << "日志系统初始化失败: " << e.what() << std::endl;
		throw;
	}
}

void MidPlayerLogger::createLogger(const MidPlayerConfig::LoggingConfig& config) {
	std::vector<spdlog::sink_ptr> sinks;

	// 控制台输出sink
	if (config.consoleOutput) {
		auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
		console_sink->set_pattern(config.consolePattern);
		sinks.push_back(console_sink);
	}

	// 文件输出sink
	if (config.fileOutput) {
		// 确保日志目录存在
		std::filesystem::path logPath(config.logFile);
		if (logPath.has_parent_path()) {
			std::filesystem::create_directories(logPath.parent_path());
		}

		spdlog::sink_ptr file_sink;

		if (config.rotationType == "daily") {
			// 按天轮转
			file_sink = std::make_shared<spdlog::sinks::daily_file_sink_mt>(
				config.logFile,
				config.rotationHour,
				config.rotationMinute
			);
		}
		else if (config.rotationType == "size") {
			// 按大小轮转
			file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
				config.logFile,
				config.maxFileSize,
				config.maxFiles
			);
		}
		else {
			// 基本文件输出
			file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(config.logFile);
		}

		file_sink->set_pattern(config.filePattern);
		sinks.push_back(file_sink);
	}

	// 创建logger
	if (sinks.empty()) {
		// 如果没有配置任何sink，至少添加控制台输出
		auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
		console_sink->set_pattern("%Y-%m-%d %H:%M:%S.%e [%t] [%^%l%$] %n - %v");
		sinks.push_back(console_sink);
	}

	logger_ = std::make_shared<spdlog::logger>("midplayer", sinks.begin(), sinks.end());

	// 设置日志级别
	setLogLevel(config.logLevel);

	// 设置刷新策略
	if (config.singleThread) {
		logger_->flush_on(spdlog::level::warn);  // 警告级别及以上立即刷新
	}
	else {
		logger_->flush_on(spdlog::level::err);   // 错误级别及以上立即刷新
	}

	// 注册为默认logger
	spdlog::set_default_logger(logger_);
}

std::shared_ptr<spdlog::logger> MidPlayerLogger::getLogger() const {
	// 使用std::call_once确保线程安全的初始化
	std::call_once(init_flag_, [this]() {
		if (!initialized_.load()) {
			const_cast<MidPlayerLogger*>(this)->initialize();
		}
	});
	return logger_;
}

void MidPlayerLogger::setLogLevel(const std::string& level) {
	setLogLevel(stringToLevel(level));
}

void MidPlayerLogger::setLogLevel(spdlog::level::level_enum level) {
	if (logger_) {
		logger_->set_level(level);
	}
}

spdlog::level::level_enum MidPlayerLogger::stringToLevel(const std::string& level) {
	std::string upperLevel = level;
	std::transform(upperLevel.begin(), upperLevel.end(), upperLevel.begin(), ::toupper);

	if (upperLevel == "TRACE") return spdlog::level::trace;
	if (upperLevel == "DEBUG") return spdlog::level::debug;
	if (upperLevel == "INFO") return spdlog::level::info;
	if (upperLevel == "WARN" || upperLevel == "WARNING") return spdlog::level::warn;
	if (upperLevel == "ERROR") return spdlog::level::err;
	if (upperLevel == "CRITICAL" || upperLevel == "FATAL") return spdlog::level::critical;
	if (upperLevel == "OFF") return spdlog::level::off;

	return spdlog::level::info;  // 默认级别
}

std::string MidPlayerLogger::levelToString(spdlog::level::level_enum level) {
	switch (level) {
	case spdlog::level::trace: return "TRACE";
	case spdlog::level::debug: return "DEBUG";
	case spdlog::level::info: return "INFO";
	case spdlog::level::warn: return "WARN";
	case spdlog::level::err: return "ERROR";
	case spdlog::level::critical: return "CRITICAL";
	case spdlog::level::off: return "OFF";
	default: return "UNKNOWN";
	}
}

void MidPlayerLogger::shutdown() {
	if (logger_) {
		logger_->flush();
		logger_.reset();
	}
	spdlog::shutdown();
	initialized_ = false;
}
