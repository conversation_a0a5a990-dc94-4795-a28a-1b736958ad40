﻿#include "stdafx.h"
#include "MidGateway.h"
#include "AtsSlotUtility.h"
#include "AtsMidParser.h"

AtsTimeStat g_startTimer;
const int MAX_RETRY_COUNT = 8;
std::atomic_int64_t g_lineCount = 0;
std::atomic_int64_t g_dealCount = 0;
std::vector<std::unique_ptr<std::atomic<int>>> thread_data_count;

class MidClientManager
{
private:
	std::mutex in_flight_mutex;
	std::atomic<bool> is_ready;
	std::atomic<std::size_t> in_flight;
	std::condition_variable in_flight_condition;
	std::vector<std::shared_ptr<MidGateway>> mid_sockets;

	std::vector<bool> stop_flags;
	std::vector<std::thread> engine_threads;
	std::unique_ptr<SlotHashRing> consistent_hash;

	std::thread result_consumer;
	std::mutex result_queue_mutex;
	std::condition_variable result_queue_cv;
	std::queue<std::map<uint64_t, std::string>> result_queue;

	std::vector<std::mutex> queue_mutexes;
	std::vector<std::queue<midRow>> queue_midrows;
	std::vector< std::map<std::string, std::map<std::string, std::queue<midRow>>>> delay_queues;
	std::vector<std::condition_variable> cv_queue_not_empty;

	struct handle_in_flight_decrement {
		MidClientManager& in_climgr;
		handle_in_flight_decrement(MidClientManager& climgr) : in_climgr(climgr) { }

		~handle_in_flight_decrement() {	// 数据处理计数减一，线程安全
			if (std::atomic_fetch_sub_explicit(&in_climgr.in_flight, std::size_t(1), std::memory_order_acq_rel) == 1) {
				std::unique_lock<std::mutex> guard(in_climgr.in_flight_mutex);
				in_climgr.in_flight_condition.notify_all();
			}
		}
	};

public:
	MidClientManager(size_t num_threads) 
		: queue_mutexes(num_threads),
		cv_queue_not_empty(num_threads),
		stop_flags(num_threads, false),
		is_ready(false),
		result_consumer(&MidClientManager::consumer_thread, this)
	{
		in_flight = 0;
		std::atomic<int> completed_connections(0);
		auto start_time = std::chrono::steady_clock::now();

		delay_queues.resize(num_threads);
		consistent_hash = std::make_unique<SlotHashRing>(num_threads);

		for (size_t i = 0; i < num_threads; ++i)
		{
			auto kdSocket = std::make_shared<MidGateway>();
			if (!kdSocket->connectToMidStdGW()) break;
			// 初始化每个线程的数据计数器
			mid_sockets.push_back(kdSocket);
			queue_midrows.push_back(std::queue<midRow>());
			thread_data_count.push_back(std::make_unique<std::atomic<int>>(0));

			completed_connections++;
			auto current_time = std::chrono::steady_clock::now();
			if (std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time).count() >= 1)
			{
				start_time = current_time;
				auto elapsed_time = std::chrono::steady_clock::now() - g_startTimer.get_start_time();
				auto elapsed_seconds = std::chrono::duration_cast<std::chrono::seconds>(elapsed_time).count();
				double progress = static_cast<double>(completed_connections) / num_threads;
				int64_t estimated_total_time = static_cast<int>(elapsed_seconds / progress);
				int64_t estimated_remaining_time = estimated_total_time - elapsed_seconds;

				std::cout << "\r" << getCurrentTime() << "连接进度: " << completed_connections << "/" << num_threads
					<< " (已用时间: " << std::fixed << std::setprecision(2) << elapsed_seconds << "秒, 预计剩余时间: "
					<< std::fixed << std::setprecision(2) << estimated_remaining_time << "秒)" << std::flush;
			}

			engine_threads.push_back(std::thread([this, i]()
			{
				while (!stop_flags[i])
				{
					// 锁定queue_mutexes[i]互斥锁
					std::unique_lock<std::mutex> lock(queue_mutexes[i]);
					auto timeout = std::chrono::steady_clock::now() + std::chrono::milliseconds(5);
					// 当前线程进入等待状态，直到条件变量cv_queue_not_empty[i]被通知，在等待期间，lock会自动解锁
					//cv_queue_not_empty[i].wait(lock, [this, i] { return !queue_midrows[i].empty() || stop_flags[i]; });
					// 在等待期间，互斥锁会自动解锁，以允许其他线程访问共享资源。一旦等待结束，无论是因为条件变量被通知还是超时，互斥锁都会重新锁定。
					cv_queue_not_empty[i].wait_until(lock, timeout, [this, i] { return !queue_midrows[i].empty() || stop_flags[i]; });

					if (!queue_midrows[i].empty())
					{
						midRow data = queue_midrows[i].front();
						queue_midrows[i].pop();
						lock.unlock();
						consistent_hash->subUserDone(i);

						std::string reqText, resText, stkText, clsText;
						if (AtsMidParser::extractReqResFromLine(data.lineNum, data.lineText, reqText, resText, stkText, clsText) && !appConfig.readonly) {
							if (appConfig.stgenable) {
								// 启用STG模拟撮合成交的功能
								if ("0B" == clsText && !delay_queues[i].contains(data.lineuser)) {
									if (!appConfig.utilmatch) {
										// 购买行为触发依赖行为检查
										delay_queues[i][data.lineuser][stkText] = std::queue<midRow>();
									}
								}
								else if ("0S" == clsText && delay_queues[i].contains(data.lineuser)) {
									// 出售行为需要检查购买结果
									if (delay_queues[i][data.lineuser].contains(stkText)) {
										// 构建查询委托请求
										if (!mid_sockets[i]->waitQuery(reqText, data.lineuser, stkText, data.lineNum)) {
											data.retryCount++;
											delay_queues[i][data.lineuser][stkText].push(data);
											continue;
										}
									}
								}
							}

							// 解析是正确的请求和应答
							mid_sockets[i]->waitAnswer(reqText, resText, data.lineNum);
							// 购买行为触发成交信息查询
							if ("0B" == clsText && appConfig.stgenable && appConfig.utilmatch) {
								int retrytime = appConfig.retrytime;
								while (retrytime--) {
									if (mid_sockets[i]->waitMatch(reqText, data.lineuser, stkText, data.lineNum))
										break;
								}
							}
						}

						// 数据处理后计数处置
						g_dealCount.fetch_add(1);
						thread_data_count[i]->fetch_add(1);
						handle_in_flight_decrement guard(*this);
					}

					//##########################################################################################
					// 遍历查询是否有缓存请求
					//##########################################################################################
					check_cache_line(i);
				}

				LOG4CXX_INFO(logger, "线程[" << i << "]退出，停止标识 " << stop_flags[i]);
			}));
		}

		is_ready = (engine_threads.size() == queue_mutexes.size());
		auto elapsed_time = std::chrono::steady_clock::now() - g_startTimer.get_start_time();
		auto elapsed_seconds = std::chrono::duration_cast<std::chrono::seconds>(elapsed_time).count();
		if (is_ready)
			std::cout << "\r" << getCurrentTime() << "所有连接已完成: " << completed_connections << "/" << num_threads << " 总计耗时: " << elapsed_seconds << "秒" << std::endl;
		else
			std::cout << "\r" << getCurrentTime() << "所有连接未完成: " << completed_connections << "/" << num_threads << " 总计耗时: " << elapsed_seconds << "秒" << std::endl;
	}

	~MidClientManager()
	{
		stop();
	}

	bool check_cache_line(size_t i)
	{
		if (appConfig.stgenable && !appConfig.utilmatch) {
			for (auto& user_queue : delay_queues[i]) {
				auto& stk_queues = user_queue.second;
				for (auto& stk_queue : stk_queues) {
					while (!stk_queue.second.empty()) {
						midRow cached_data = stk_queue.second.front();
						stk_queue.second.pop();

						std::string reqText, resText, stkText, clsText;
						if (AtsMidParser::extractReqResFromLine(cached_data.lineNum, cached_data.lineText, reqText, resText, stkText, clsText)) {
							if (!appConfig.readonly) {
								if (cached_data.retryCount >= appConfig.retrytime || mid_sockets[i]->waitQuery(reqText, cached_data.lineuser, stkText, cached_data.lineNum)) {
									// 超过重试次数或有持仓，正常发出请求
									mid_sockets[i]->waitAnswer(reqText, resText, cached_data.lineNum);
									g_dealCount.fetch_add(1);
									thread_data_count[i]->fetch_add(1);
									handle_in_flight_decrement guard(*this);
								}
								else {
									// 没有持仓，重新缓存
									cached_data.retryCount++;
									stk_queue.second.push(cached_data);
								}
							}
						}
					}
				}
			}
		}

		return true;
	}

	void pushData(const std::string& user_code, const std::string& func_code, const std::string& mid_data, uint64_t num) {
		// 记录上次分配的线程号
		static size_t last_thread = 0;
		size_t hash_thread = last_thread;
		if (is_ready) {
			// last_thread = (last_thread + 1) % row_queues.size();
			hash_thread = consistent_hash->getThreadForCustomer(user_code);
			{
				// 确保单客户请求串行处理
				std::lock_guard<std::mutex> lock(queue_mutexes[hash_thread]);
				queue_midrows[hash_thread].push({ num, 0, user_code, mid_data });
				LOG4CXX_DEBUG(logger, "用户[" << (user_code.empty() ? func_code : user_code) << "]分配线程号：" << hash_thread << ", 文件行号：" << num);
			}

			// 通知线程有新请求数据可处理
			std::atomic_fetch_add_explicit(&in_flight, 1, std::memory_order_relaxed);
			cv_queue_not_empty[hash_thread].notify_all();
		}
		else {
			static std::once_flag oc;
			std::call_once(oc, [&]() {
				LOG4CXX_FATAL(logger, "MID网关连接失败，数据将不会被处理...");
			});
			LOG4CXX_ERROR(logger, "用户[" << (user_code.empty() ? func_code : user_code) << "]请求数据不到网关");

			// 数据处理后计数处置
			g_dealCount.fetch_add(1);
			handle_in_flight_decrement guard(*this);
		}
	}

	void wait_until_nothing_in_flight() {
		do
		{
			int total_caches = 0;
			for (size_t i = 0; i < engine_threads.size(); ++i) {
				// 确保线程安全通知处理
				std::lock_guard<std::mutex> lock(queue_mutexes[i]);
				auto outerMap = delay_queues[i];
				for (auto& pair : outerMap) {
					for (auto& innerPair : pair.second) {
						total_caches += innerPair.second.size();
					}
				}
				cv_queue_not_empty[i].notify_all();
			}

			if (0 == total_caches) break;
			std::this_thread::sleep_for(std::chrono::milliseconds(500));
		} while (appConfig.stgenable && appConfig.utilmatch);

		// 等待所有的任务已经处理完成
		std::unique_lock<std::mutex> lock(in_flight_mutex);
		in_flight_condition.wait(lock, [this] { return this->in_flight == 0; });

		// 所有线程都已经处理完成数据，以追加模式打开文件
		std::map<uint64_t, std::string> anserData;
		{
			std::lock_guard<std::mutex> guard(MidGateway::g_dataMutex);
			std::swap(anserData, MidGateway::g_anserData);
		}

		write_report();
		// 重置负载均衡分配路径
		consistent_hash->resetBalance();
	}

	void adjustBalance() {
		if (is_ready) consistent_hash->adjustBalance(queue_midrows, queue_mutexes, cv_queue_not_empty);
	}

	void redoBalance() {
		write_report();
		if (is_ready) consistent_hash->redoBalance();
	}

	void write_report() {

		std::map<uint64_t, std::string> anserData;
		{
			std::lock_guard<std::mutex> guard(MidGateway::g_dataMutex);
			if (0 == MidGateway::g_anserData.size())
				return;
			std::swap(anserData, MidGateway::g_anserData);
		}

		{
			std::lock_guard<std::mutex> lock(result_queue_mutex);
			result_queue.push(std::move(anserData));
		}
		result_queue_cv.notify_one();

		//// 所有线程都已经处理完成数据，以追加模式打开文件
		//std::map<uint64_t, std::string> anserData;
		//{
		//	std::lock_guard<std::mutex> guard(MidGateway::g_dataMutex);
		//	if (0 == MidGateway::g_anserData.size())
		//		return;
		//	std::swap(anserData, MidGateway::g_anserData);
		//}

		//std::thread([filename = appConfig.filename, anserData = std::move(anserData)]() {
		//	std::ofstream outstreamFile(filename, std::ios::app);
		//	if (!outstreamFile.is_open()) {
		//		LOG4CXX_ERROR(logger, "Error opening log file: " << filename);
		//		return;
		//	}
		//	for (const auto& entry : anserData) {
		//		outstreamFile << entry.second << std::endl;
		//	}
		//	outstreamFile.close();
		//}).detach();
	}

	void consumer_thread() {
		while (!stop_flags[0]) {
			std::map<uint64_t, std::string> anserData;
			{
				std::unique_lock<std::mutex> lock(result_queue_mutex);
				result_queue_cv.wait(lock, [this] { return !result_queue.empty() || stop_flags[0]; });
				if (!stop_flags[0] && result_queue.empty()) {
					break;
				}
				anserData = std::move(result_queue.front());
				result_queue.pop();
			}

			std::ofstream outstreamFile(appConfig.filename, std::ios::app);
			if (!outstreamFile.is_open()) {
				LOG4CXX_ERROR(logger, "Error opening log file: " << appConfig.filename);
				continue;
			}
			for (const auto& entry : anserData) {
				outstreamFile << entry.second << std::endl;
			}
			outstreamFile.close();
		}
	}

	bool ready() {
		return is_ready;
	}

	void stop() {
		for (size_t i = 0; i < engine_threads.size(); ++i) {
			{// 确保线程安全通知退出
				std::lock_guard<std::mutex> lock(queue_mutexes[i]);
				stop_flags[i] = true;
				cv_queue_not_empty[i].notify_all();
			}

			if (engine_threads[i].joinable())
				engine_threads[i].join();
		}

		for (auto& queue : queue_midrows) {
			while (!queue.empty())
				queue.pop();
		}

		for (auto& outerMap : delay_queues) {
			for (auto& pair : outerMap) {
				for (auto& innerPair : pair.second) {
					innerPair.second = std::queue<midRow>();
				}
			}
		}

		for (auto& socket : mid_sockets) {
			socket->disconnect();
		}

		result_queue_cv.notify_all();
		if (result_consumer.joinable()) {
			result_consumer.join();
		}
	}
};

std::unique_ptr<MidClientManager> gclientPool = nullptr;

std::string AtsMidParser::getSystemErrorMessage()
{
	LPSTR buffer = nullptr;
	DWORD dwCode = ::GetLastError();
	auto status = ::FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
		nullptr, dwCode, 0, (LPSTR)&buffer, 0, nullptr);

	if (status != 0 && buffer != nullptr) {
		m_sysMesage = buffer;
		LocalFree(buffer);
	}
	else {
		m_sysMesage = "Failed to retrieve error message.";
	}

	return m_sysMesage;
}

std::string AtsMidParser::convertToTimestamp(double time_tick)
{
	using namespace std::chrono;

	// 将time_tick分解为整数部分和小数部分
	auto sec = static_cast<long long>(time_tick);
	auto subsec = static_cast<long long>((time_tick - sec) * 1'000'000'000);

	// 创建一个time_point
	system_clock::time_point tp{ duration_cast<system_clock::duration>(seconds(sec) + nanoseconds(subsec)) };

	// 转换为time_t和subseconds
	auto tt = system_clock::to_time_t(tp);
	auto subsec_remainder = duration_cast<nanoseconds>(tp.time_since_epoch()) % seconds(1);

	// 使用strftime格式化日期和时间
	std::stringstream ss;
	ss << std::put_time(std::localtime(&tt), "%Y-%m-%d %H:%M:%S");
	ss << '.' << std::setfill('0') << std::setw(9) << subsec_remainder.count();

	return ss.str();
}

bool AtsMidParser::isValidMidRequest(uint64_t line, std::string& row)
{
	if (row.length() < 28) {
		LOG4CXX_TRACE(logger, "无效请求[长度不符]行号：" << line << ", 内容：" << row);
		return false;
	}

	uint32_t uheadLen = std::atoi(row.substr(0, 4).c_str());
	uint32_t udataLen = std::atoi(row.substr(5, 4).c_str());
	uint32_t utotalLen = uheadLen + udataLen;
	if (utotalLen > row.length()) {
		LOG4CXX_TRACE(logger, "无效请求[残缺报文]行号：" << line << ", 内容：" << row);
		return false;
	}
	else if (utotalLen < row.length()) {
		row = row.substr(0, utotalLen);
	}

	if (row.back() != '|') {
		LOG4CXX_TRACE(logger, "无效请求[内容不匹配]行号：" << line << ", 内容：" << row);
		return false;
	}

	return true;
}

bool AtsMidParser::extractReqRes(uint64_t line, const std::string& row, std::string& req, std::string& res)
{
	const char* data = row.c_str();
	const char* reqPos = std::strstr(data, appConfig.reqmflag.c_str());
	if (!reqPos) 	return false;
	reqPos += 4;

	std::string userCodeReq, funcCodeReq;
	if (!extractFields(reqPos, appConfig.userfiled, appConfig.funcfiled, userCodeReq, funcCodeReq)) {
		LOG4CXX_ERROR(logger, "Error extracting fields from REQ at line " << line);
		return false;
	}
	LOG4CXX_TRACE(logger, "REQ User Code: " << userCodeReq << ", Function Code: " << funcCodeReq);

	// 如果存在，处理RESP部分
	const char* respPos = strstr(data, appConfig.ansmflag.c_str());
	if (respPos) {
		req = row.substr(reqPos - data, respPos - reqPos - 1);

		respPos += appConfig.ansmflag.length();

		std::string resultCodeResp, resultDetailResp;
		if (!extractFields(respPos, appConfig.returnfiled, appConfig.returnfiled + 1, resultCodeResp, resultDetailResp)) {
			LOG4CXX_ERROR(logger, "Error extracting fields from RESP at line " << line);
			return false;
		}

		if (respPos && *respPos) res = respPos;
		LOG4CXX_TRACE(logger, "RESP User Code: " << resultCodeResp << ", Function Code: " << resultDetailResp);
	}
	else {
		req = row.substr((reqPos - data), row.size());
	}

	return isValidMidRequest(line, req);
}

bool AtsMidParser::extractReqResFromLine(uint64_t line, const std::string& row, std::string& req, std::string& res, std::string& stk, std::string& cls)
{
	const char* data = row.c_str();
	const char* reqPos = std::strstr(data, appConfig.reqmflag.c_str());
	if (!reqPos) 	return false;
	reqPos += 4;

	std::string userCodeReq, funcCodeReq;
	if (!extractFields(reqPos, appConfig.userfiled, appConfig.funcfiled, userCodeReq, funcCodeReq)) {
		LOG4CXX_ERROR(logger, "Error extracting fields from REQ at line " << line);
		return false;
	}

	// 如果存在，处理RESP部分
	const char* respPos = strstr(data, appConfig.ansmflag.c_str());
	if (respPos) {
		req = row.substr(reqPos - data, respPos - reqPos - 1);
		// 识别委托买卖业务
		extractFields(reqPos, appConfig.stkcfiled, appConfig.typefiled, stk, cls);

		respPos += appConfig.ansmflag.length();
		if (respPos && *respPos) res = respPos;
	}
	else {
		req = row.substr((reqPos - data), row.size());
	}

	return isValidMidRequest(line, req);
}

std::string AtsMidParser::extractFields(uint64_t line, const std::string& row, int field)
{
	std::string value;
	extractFields(line, row, field, value);
	return value;
}

bool AtsMidParser::extractFields(uint64_t line, const std::string& row, int field, std::string& val)
{
	int fieldIndex = 0;
	const char* current = row.c_str();

	// 查找用户号（第N个字段）
	while (fieldIndex < field && *current) {
		if (*current == '|') {
			++fieldIndex;
			if (fieldIndex == field - 1) {
				++current;
				val.assign(current, strcspn(current, "|"));
				break;
			}
		}
		++current;
	}

	return fieldIndex == field - 1;
}

bool AtsMidParser::extractFields(const char* data, int codeField, int detailField, std::string& codeValue, std::string& detailValue)
{
	int fieldIndex = 0;
	const char* current = data;

	// 查找用户号（第N个字段）
	while (fieldIndex < codeField && *current) {
		if (*current == '|') {
			++fieldIndex;
			if (fieldIndex == codeField - 1) {
				++current;
				codeValue.assign(current, strcspn(current, "|"));
				break;
			}
		}
		++current;
	}
	if (fieldIndex != codeField - 1) return false;

	// 继续查找功能号（第M个字段）
	while (fieldIndex < detailField && *current) {
		if (*current == '|') {
			++fieldIndex;
			if (fieldIndex == detailField - 1) {
				++current;
				detailValue.assign(current, strcspn(current, "|"));
				break;
			}
		}
		++current;
	}
	return fieldIndex == detailField - 1;
}

bool AtsMidParser::handleCompleteMidLine(const std::string& line, uint64_t pos, uint64_t num)
{
	const char* data = line.c_str();
	const char* reqPos = std::strstr(data, "REQ:");
	if (!reqPos) {
		LOG4CXX_ERROR(logger, "REQ not found in the line:" << num << ", content: " << line);
		return false;
	}
	reqPos += 4;

	std::string userCodeReq, funcCodeReq;
	if (!extractFields(reqPos, appConfig.userfiled, appConfig.funcfiled, userCodeReq, funcCodeReq)) {
		LOG4CXX_ERROR(logger, "Error extracting fields from REQ at line " << num);
		return false;
	}

	if (0 == g_startTimer.current() % 2) printWorkerProgress(num, g_dealCount);
	LOG4CXX_TRACE(logger, "REQ User Code: " << userCodeReq << ", Function Code: " << funcCodeReq);

	// 委托过滤报文
	if (!appConfig.markets.empty() && "403" == funcCodeReq) {
		std::string market = extractFields(num, line, 15);
		if (appConfig.markets.contains(market)) {
			return true;
		}
	}
	if (appConfig.funclbm.contains(funcCodeReq) || 0 == appConfig.debuglevel) {
		return true;
	}

	gclientPool->pushData(userCodeReq, funcCodeReq, line, num);

	if (0 == ++g_lineCount % appConfig.batchnum) {
		if (gclientPool->ready()) {
			// 设置文本颜色为蓝色
			SetConsoleTextAttribute(m_hConsole, FOREGROUND_GREEN | FOREGROUND_INTENSITY);

			bool firstBalance = false;
			int64_t busyCount = g_lineCount - g_dealCount;
			while (busyCount > 0) {
				std::this_thread::sleep_for(std::chrono::milliseconds(10));
				printWorkerProgress(num, g_dealCount);
				busyCount = g_lineCount - g_dealCount;

				// 检测到待处理数据小于1/5后做一次动态负载均衡
				if (busyCount < appConfig.batchnum / 5 && false == firstBalance) {
					firstBalance = !firstBalance;
					gclientPool->adjustBalance();
				} else if (busyCount < appConfig.batchnum / 10 && true == firstBalance) {
					if (0 == g_startTimer.current() % 10) {
						gclientPool->redoBalance();
						SetConsoleTextAttribute(m_hConsole, m_oriAttributes);
						return true;
					}
				}
			}

			// 等待所有任务处理完成
			printWorkerProgress(num, g_dealCount);
			gclientPool->wait_until_nothing_in_flight();
			// 还原原来的控制台属性
			SetConsoleTextAttribute(m_hConsole, m_oriAttributes);
		}
	}

	// 如果批量处理数目巨大，分批次
	if (1000000 < appConfig.batchnum && 0 == g_dealCount % 100000) {
		gclientPool->write_report();
	}

	return true;
}

void AtsMidParser::handleOneIllegalMidLine(const std::string& line, int64_t pos, uint64_t num)
{
	LOG4CXX_ERROR(logger, "Incomplete line detected at position:" << pos << ", line: " << num << ", content: " << line);
}

void AtsMidParser::printWorkerProgress(uint64_t& numLine, uint64_t dealLine)
{
	static size_t current_thread_group = 0; // 当前显示的线程组
	static auto last_switch_time = std::chrono::steady_clock::now(); // 上次切换时间

	auto current_time = std::chrono::steady_clock::now();
	if (std::chrono::duration_cast<std::chrono::seconds>(current_time - m_starTtime).count() >= 1) {
		m_speedLine = (dealLine - m_dealLine) / std::chrono::duration_cast<std::chrono::seconds>(current_time - m_starTtime).count();
		m_dealLine = dealLine;
		m_starTtime = current_time;
	}

	if (gclientPool->ready())
	{
		std::ostringstream oss;
		oss << "\r" << g_dealCount << "/" << numLine << " " << std::setw(4) << std::setfill('0') << m_speedLine;

		// 每次显示 4 个线程的数据
		size_t threads_per_group = 4;
		size_t total_groups = (thread_data_count.size() + threads_per_group - 1) / threads_per_group;

		// 检查是否需要切换显示的线程组
		if (std::chrono::duration_cast<std::chrono::seconds>(current_time - last_switch_time).count() >= 2) {
			current_thread_group = (current_thread_group + 1) % total_groups;
			last_switch_time = current_time;
		}

		// 显示当前线程组的数据
		size_t start_index = current_thread_group * threads_per_group;
		size_t end_index = (std::min)(start_index + threads_per_group, thread_data_count.size());
		for (size_t i = start_index; i < end_index; ++i) {
			oss << " | 线程[" << std::setw(3) << std::setfill('0') << i << "]: " << thread_data_count[i]->load();
		}

		std::cout << oss.str() << std::flush;
	}
}

void AtsMidParser::printReplayProgress(uint64_t& numLine, uint64_t& dealLine, uint64_t& readPos, LARGE_INTEGER& fileSize)
{
	auto current_time = std::chrono::steady_clock::now();
	if (m_showsp && std::chrono::duration_cast<std::chrono::seconds>(current_time - m_starTtime).count() >= 1) {
		auto num_persec = (numLine - dealLine) / std::chrono::duration_cast<std::chrono::seconds>(current_time - m_starTtime).count();
		dealLine = numLine;
		m_starTtime = current_time;

		// 设置文本颜色为蓝色
		SetConsoleTextAttribute(m_hConsole, FOREGROUND_BLUE | FOREGROUND_INTENSITY);

		// 打印进度信息
		std::cout << getCurrentTime() << "处理进度: " << std::fixed << std::setprecision(3) << 100.0 * readPos / fileSize.QuadPart
			<< "%，当前处理行号：" << numLine << "，处理速度：" << num_persec << " 已用时间: " << g_startTimer.current() << "秒" << std::endl;

		// 还原原来的控制台属性
		SetConsoleTextAttribute(m_hConsole, m_oriAttributes);
	}
}

/**************************************************************************************************************************
ipdst:*********,REQ:0059|0003|68f75ad3|KDGATEWAY1.2||**********||U|||6a:4cd|74|90|
ipdst:*********,REQ:0100|0006|91d0b400|KDGATEWAY1.2|102551632|FSTORE-MFSTORE--1715330890497||8||1|8A1G2xgGR8J_1|DEFAULT|523|||
ipdst:*********,REQ:0107|0015|8030dcdb|KDGATEWAY1.2|106650265|AP-sjzt-***********-18159520059||8||1|8A1G_8A1G2CIN2oM_1|DEFAULT|302|106650265||
ipdst:*********,REQ:0107|0016|36abf2b0|KDGATEWAY1.2|106650265|AP-sjzt-***********-18159520059||8||1|8A1G_8A1G2CIN2oM_2|DEFAULT|501|106650265|||
ipdst:*********,REQ:0058|0004|95d8b980|KDGATEWAY1.2||20677CD44028||L||1|3221||202|
**************************************************************************************************************************/
bool AtsMidParser::win_readNetis_Bymmap(const std::string& filename)
{
	g_startTimer.reset();
	if (!gclientPool) gclientPool = std::make_unique<MidClientManager>(appConfig.threadnum);
	if (!gclientPool->ready()) gclientPool->stop();

	// Save current console attributes
	CONSOLE_SCREEN_BUFFER_INFO consoleInfo;
	m_hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
	GetConsoleScreenBufferInfo(m_hConsole, &consoleInfo);
	m_oriAttributes = consoleInfo.wAttributes;

	// Convert std::string to wide string
	std::wstring wfilename(filename.begin(), filename.end());
	HANDLE fileHandle = CreateFile(wfilename.c_str(), GENERIC_READ, FILE_SHARE_READ, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
	if (fileHandle == INVALID_HANDLE_VALUE) {
		LOG4CXX_ERROR(logger, "Failed to open file: " << getSystemErrorMessage());
		std::cout << getCurrentTime() << "Failed to open log file: " << getSystemErrorMessage() << std::endl;
		return false;
	}

	LARGE_INTEGER fileSize;
	if (!GetFileSizeEx(fileHandle, &fileSize)) {
		CloseHandle(fileHandle);
		LOG4CXX_ERROR(logger, "Failed to get file size: " << getSystemErrorMessage());
		return false;
	}

	HANDLE fileMapping = CreateFileMapping(fileHandle, nullptr, PAGE_READONLY, 0, 0, nullptr);
	if (fileMapping == nullptr) {
		CloseHandle(fileHandle);
		LOG4CXX_ERROR(logger, "Failed to create file mapping: " << getSystemErrorMessage());
		return false;
	}

	uint64_t readPos = 0;
	uint64_t numLine = 0;
	uint64_t dealLine = 0;
	std::vector<char> leftover;

	// 分段读取文件，每次1GB，需要处理上一个块的剩余内容
	const size_t blockSize = 1024 * 1024 * 1024;
	size_t blockCount = fileSize.QuadPart / blockSize + (fileSize.QuadPart % blockSize == 0 ? 0 : 1);
	for (size_t i = 0; i < blockCount; ++i) {
		size_t offset = i * blockSize;
		size_t readSize = (i == blockCount - 1) ? fileSize.QuadPart % blockSize : blockSize;
		// 当文件大小正好是块大小的倍数时，最后一次循环的readSize将会是0，这可能会导致MapViewOfFileEx失败。
		if (readSize == 0) {
			break;
		}

		// 当偏移量或读取大小超过DWORD的最大值4GB时，MapViewOfFile会出现数据错误，映射的数据从头读取
		LPVOID fileData = MapViewOfFileEx(fileMapping, FILE_MAP_READ, offset >> 32, offset & 0xFFFFFFFF, readSize, nullptr);
		if (fileData == nullptr) {
			LOG4CXX_ERROR(logger, "Failed to map file to memory: " << getSystemErrorMessage());
			CloseHandle(fileMapping);
			CloseHandle(fileHandle);
			return false;
		}

		// 将上一个块的剩余内容添加到当前行的开始
		m_starTtime = std::chrono::steady_clock::now();
		std::vector<char> currentLine = leftover;
		leftover.clear();

		char* dataStart = static_cast<char*>(fileData);
		char* dataEnd = dataStart + static_cast<size_t>(readSize);
		char* lineStart = dataStart;
		char* lineEnd = static_cast<char*>(ats::memchr_optimized(lineStart, dataEnd, '\n'));

		while (lineEnd != nullptr && lineEnd < dataEnd) {
			++numLine;
			// 添加当前行的内容
			readPos += lineEnd - lineStart + 1;
			currentLine.insert(currentLine.end(), lineStart, lineEnd);
			// 检查当前行的最后一个字符是否是'\r'，如果是，就将其删除
			if (!currentLine.empty() && currentLine.back() == '\r')
				currentLine.pop_back();

			// 检查报文完整性
			if (!currentLine.empty()) {
				if (currentLine.back() == '|') {
					// 报文完整，处理当前行
					handleCompleteMidLine(std::string(currentLine.begin(), currentLine.end()), readPos, numLine);
					currentLine.clear();
				}
				else {
					// 报文不完整，尝试查看下一行是否为新报文的开始
					lineStart = lineEnd + 1;
					if (lineStart != dataEnd) {
						// 读取下一行报文内容
						lineEnd = static_cast<char*>(ats::memchr_optimized(lineStart, dataEnd, '\n'));
						std::string nextLine(lineStart, lineEnd);

						// 检查下一行是否以ipdst开头，表示新的报文开始
						if (nextLine.find(appConfig.configers["main"]["begin_mid"]) == 0) {
							// 记录或处理异常行（当前不完整的行）
							handleOneIllegalMidLine(std::string(currentLine.begin(), currentLine.end()), readPos, numLine);
							currentLine.clear();
							continue; // 继续处理新行
						}
						else {
							// 跳过后续处理，继续下一次循环
							continue;
						}
					}
					else {
						// 已达文件末尾且报文不完整，处理或记录异常
						handleOneIllegalMidLine(std::string(currentLine.begin(), currentLine.end()), readPos, numLine);
						break; // 文件结束，跳出循环
					}
				}
			}

			lineStart = lineEnd + 1;
			lineEnd = static_cast<char*>(ats::memchr_optimized(lineStart, dataEnd, '\n'));
			printReplayProgress(numLine, dealLine, readPos, fileSize);
		}

		// 如果最后一行不完整，将其添加到leftover中
		if (lineStart != dataEnd) {
			readPos += dataEnd - lineStart;
			leftover.insert(leftover.end(), lineStart, dataEnd);
		}

		//################################################################################################
		// 释放内存映射
		//################################################################################################
		UnmapViewOfFile(fileData);
	}

	// 处理最后一个可能的不完整行，特别是当文件只有一行且无换行符时
	if (!leftover.empty()) {
		// 这里直接处理lastover中的内容，因为它代表的是最后一行，可能是唯一的行
		LOG4CXX_INFO(logger, "末行[" << numLine+1 << "]处理，遇到无换行符");
		handleCompleteMidLine(std::string(leftover.begin(), leftover.end()), readPos, ++numLine);
	}

	CloseHandle(fileMapping);
	CloseHandle(fileHandle);
	std::cout << getCurrentTime() << "处理进度: 100%，当前处理行号：" << numLine << " 已用时间: " << g_startTimer.current() << "秒" << std::endl;

	g_startTimer.print(getCurrentTime().append("readNetis"));
	while (g_lineCount != g_dealCount) {
		printWorkerProgress(numLine, g_dealCount);
		std::this_thread::sleep_for(std::chrono::milliseconds(10));
	}
	g_startTimer.print(getCurrentTime().append("paseNetis"));

	return true;
}

bool AtsMidParser::linux_readNetis_Bymmap(const std::string& filename)
{
#ifndef _MSC_VER
	int fileHandle = open(filename.c_str(), O_RDONLY);
	if (!fileHandle) {
		std::cerr << "Failed to open file." << std::endl;
		return false;
	}

	struct stat sb;
	fstat(fileHandle, &sb);

	char* fileData = (char*)mmap(NULL, sb.st_size, PROT_READ, MAP_PRIVATE, fileHandle, 0);
	close(fileHandle);
	if (fileData == NULL) {
		std::cerr << "Failed to map file to memory." << std::endl;
		return false;
	}

	string rowLine;
	int64_t numLine = 0;
	for (int i = 0; i < sb.st_size; i++) {
		if (fileData[i] == '\n') {
			processRow(rowLine, numLine++);
			rowLine.clear();
		}
		else {
			rowLine += fileData[i];
		}
	}

	munmap(fileData, sb.st_size);
#endif
	return true;
}