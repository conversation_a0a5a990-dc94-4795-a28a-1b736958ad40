﻿#include "stdafx.h"
#include "crc.h"
#include "Blowfish.h"
#include "MiniSocket.h"
#include <intrin.h> 
#include <emmintrin.h>

#define MAIN_SEC "main"
#define MAIN_ONLY "read_only"
#define MAIN_STG "stg_enable"
#define MAIN_MACH "util_match"
#define MAIN_BEGN "begin_mid"
#define MAIN_RTRY "retry_count"
#define MAIN_MEM "memchr_algo"
#define MAIN_USER "user_filed"
#define MAIN_FUNC "func_filed"
#define MAIN_STKC "stkc_filed"
#define MAIN_TYPE "type_filed"

#define MAIN_CODE "code_filed"
#define MAIN_DEBG "debug_level"
#define MAIN_BACH "batch_num"
#define MAIN_THRD "thread_num"

#define KDGW_STD "kdg_std"
#define KDGW_CHK "checkin"

#define KDGW_MID "mid_cfg"
#define KDGW_REQ "req_flag"
#define KDGW_ANS "ans_flag"
#define KDGW_LBM "skip_func"
#define KDGW_MKT "skip_market"

AplicationConfig appConfig;

namespace ats
{
	void reset_elapsed_time()
	{
		appConfig.clock = std::chrono::high_resolution_clock::now();
	}

	// 计算并打印代码块执行耗时的辅助函数
	void print_elapsed_time(const char* actionDescription)
	{
		using namespace std::chrono;

		// 记录结束时间
		auto end_cloker = std::chrono::high_resolution_clock::now();

		// 计算耗时
		duration<double, std::milli> ms = end_cloker - appConfig.clock; // 总毫秒数
		auto micros = duration_cast<microseconds>(end_cloker - appConfig.clock).count() % 1000; // 微秒部分

		// 计算时分秒毫秒，注意正确转换和使用duration
		auto total_seconds = duration_cast<seconds>(ms);
		ms -= total_seconds;
		auto total_millis = duration_cast<milliseconds>(ms);
		ms -= total_millis;

		int hours = static_cast<int>(total_seconds.count() / 3600);
		total_seconds -= hours * seconds(3600);
		int minutes = static_cast<int>(total_seconds.count() / 60);
		total_seconds -= minutes * seconds(60);
		int seconds = static_cast<int>(total_seconds.count());
		int millis = static_cast<int>(total_millis.count());

		appConfig.clock = std::chrono::high_resolution_clock::now();
	}

	void* memchr_optimized(char* ptr, char* end, int value)
	{
		size_t num = end - ptr;
		switch (appConfig.memchr)
		{
		case 0:
			return fast_memchr(ptr, value, num);

		case 1:
			return sse2_memchr(ptr, value, num);

		case 2:
			return std::find(ptr, end, value);

		default:
			return std::memchr(ptr, value, num);
		}

		// SSE2版本的memchr函数
		const char* p = (const char*)ptr;
		__m128i v = _mm_set1_epi8(value);

		for (; ((uintptr_t)p & 15) && num; --num, ++p) {
			if (*p == value) {
				return (void*)p;
			}
		}

		for (; num >= 16; num -= 16, p += 16) {
			__m128i mem = _mm_load_si128((const __m128i*)p);
			__m128i cmp = _mm_cmpeq_epi8(mem, v);
			int mask = _mm_movemask_epi8(cmp);
			if (mask) {
				unsigned long index;
				_BitScanForward(&index, mask);
				return (void*)(p + index);
			}
		}

		for (; num; --num, ++p) {
			if (*p == value) {
				return (void*)p;
			}
		}

		return nullptr;
	}

	IniValMap parse_config_file(const std::string& filename)
	{
		IniValMap iniData;
		std::ifstream file(filename);
		if (!file.is_open()) {
			std::cerr << "无法打开文件: " << filename << std::endl;
			return iniData;
		}

		std::string line;
		std::string currentSection;
		while (std::getline(file, line)) {
			// 去除行首和行尾的空格和制表符
			line.erase(0, line.find_first_not_of(" \t"));
			line.erase(line.find_last_not_of(" \t") + 1);

			if (line.empty() || line[0] == ';' || line[0] == '#') {
				// 空行或注释行，忽略
				continue;
			}
			else if (line[0] == '[' && line[line.length() - 1] == ']') {
				// 部分行，提取部分名称
				currentSection = line.substr(1, line.length() - 2);
			}
			else {
				// 键值对行
				size_t delimiterPos = line.find('=');
				if (delimiterPos == std::string::npos) {
					throw std::runtime_error("Invalid line in ini file: " + line);
				}

				// 提取键和值
				std::string key = line.substr(0, delimiterPos);
				std::string value = line.substr(delimiterPos + 1);

				// 去除键和值周围的空格和制表符
				key.erase(0, key.find_first_not_of(" \t"));
				key.erase(key.find_last_not_of(" \t") + 1);
				value.erase(0, value.find_first_not_of(" \t"));
				value.erase(value.find_last_not_of(" \t") + 1);

				// 将键值对添加到当前部分
				iniData[currentSection][key] = value;
			}
		}

		file.close();

		// 默认配置一个值
		if (!iniData.contains(MAIN_SEC))
			iniData[MAIN_SEC][MAIN_BEGN] = "ipdst:";
		if (!iniData[MAIN_SEC].contains(MAIN_MEM))
			iniData[MAIN_SEC][MAIN_MEM] = "find";
		if (!iniData[MAIN_SEC].contains(MAIN_ONLY))
			iniData[MAIN_SEC][MAIN_ONLY] = "false";
		if (!iniData[MAIN_SEC].contains(MAIN_STG))
			iniData[MAIN_SEC][MAIN_STG] = "true";
		if (!iniData[MAIN_SEC].contains(MAIN_MACH))
			iniData[MAIN_SEC][MAIN_MACH] = "true";

		// MID报文配置值
		if (!iniData.contains(KDGW_MID))
			iniData[KDGW_MID][KDGW_REQ] = "REQ:";
		if (!iniData[KDGW_MID].contains(KDGW_ANS))
			iniData[KDGW_MID][KDGW_ANS] = "RESP:";
		if (!iniData[KDGW_MID].contains(KDGW_LBM))
			iniData[KDGW_MID][KDGW_LBM] = "100,301";
		if (!iniData[KDGW_MID].contains(KDGW_MKT))
			iniData[KDGW_MID][KDGW_MKT] = "20,30";		
		
		// MID网关配置值
		if (!iniData.contains(KDGW_STD))
			iniData[KDGW_STD][KDGW_CHK] = "0000|0000|CRCCRCCR|KDGATEWAY1.2|9988|127.0.0.1|999|4|SS|2|";

		return iniData;
	}

	// 获取系统临时目录的路径
	std::string getTempDirectoryPath() {
		const char* tempDir = std::getenv("TEMP"); // Windows环境下
		if (!tempDir) {
			tempDir = std::getenv("TMP"); // 或者Linux/macOS环境下的TMP
		}
		if (!tempDir) {
			throw std::runtime_error("无法获取临时目录的路径");
		}
		return std::string(tempDir);
	}

	// 保存最近处理的文件路径到临时目录中的文件
	void saveLastProcessedFile(const std::string& filePath) {
		std::string tempFilePath = getTempDirectoryPath() + "/last_processed_file.txt";
		std::ofstream file(tempFilePath);
		if (file.is_open()) {
			file << filePath << std::endl;
			file.close();
		}
		else {
			std::cerr << "无法写入最近处理的文件记录到临时目录。" << std::endl;
		}
	}

	// 获取上次处理的文件路径，假定存储在临时目录
	std::optional<std::string> getLastProcessedFile() {
		std::string tempFilePath = getTempDirectoryPath() + "/last_processed_file.txt";
		std::ifstream file(tempFilePath);
		if (file) {
			std::string fileName;
			file >> fileName;
			if (!fileName.empty()) {
				return fileName;
			}
		}
		return {};
	}

	void init_application(int argc, char* argv[])
	{
		CRC_Init();
		// 全局多键值对映射表
		std::map<std::string, int> globalMap = { {"fast", 0},{"sse2", 1},{"find", 2},{"std", 3} };
		// 获取当前程序的执行路径
		std::filesystem::path exePath = std::filesystem::absolute(std::filesystem::path(argv[0]));
		// 获取执行路径的父目录作为工作目录
		std::filesystem::current_path(exePath.parent_path());

		HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
		SetConsoleTextAttribute(hConsole, FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE);

		// 检查并创建 config 目录
		std::filesystem::path configPath = "config";
		if (!std::filesystem::exists(configPath)) std::filesystem::create_directory(configPath);

		// 如果"AtsMidSimulator.ini"文件不存在，则从可执行文件中提取该文件
		if (!std::filesystem::exists("AtsMidSimulator.ini"))
			AxCommon::ExtractFileFromExecutable(IDR_CONFIG, "AtsMidSimulator.ini");

		// 解析配置文件并设置相关配置
		appConfig.configers = parse_config_file("AtsMidSimulator.ini");
		appConfig.startflag = appConfig.configers[MAIN_SEC][MAIN_BEGN];
		appConfig.readonly = appConfig.configers[MAIN_SEC][MAIN_ONLY] == "true";
		appConfig.stgenable = appConfig.configers[MAIN_SEC][MAIN_STG] == "true";		
		appConfig.utilmatch = appConfig.configers[MAIN_SEC][MAIN_MACH] == "true";

		// 如果全局映射表中包含配置文件中的内存选项，则设置内存选项对应的值
		if (globalMap.contains(appConfig.configers[MAIN_SEC][MAIN_MEM]))
			appConfig.memchr = globalMap[appConfig.configers[MAIN_SEC][MAIN_MEM]];

		// 如果配置文件中包含签入选项，则设置签入选项对应的值
		if (appConfig.configers[KDGW_STD].contains(KDGW_CHK))
			appConfig.checkins = appConfig.configers[KDGW_STD][KDGW_CHK];

		// 如果配置文件中包含请求分割，则设置请求分割对应的值
		if (appConfig.configers[KDGW_MID].contains(KDGW_REQ))
			appConfig.reqmflag = appConfig.configers[KDGW_MID][KDGW_REQ];

		// 如果配置文件中包含应答分割，则设置应答分割对应的值
		if (appConfig.configers[KDGW_MID].contains(KDGW_ANS))
			appConfig.ansmflag = appConfig.configers[KDGW_MID][KDGW_ANS];

		// 如果配置文件中包含调试选项，则将调试选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_DEBG))
			appConfig.debuglevel = std::atol(appConfig.configers[MAIN_SEC][MAIN_DEBG].c_str());

		// 如果配置文件中包含重试选项，则将调试选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_RTRY))
			appConfig.retrytime = std::atol(appConfig.configers[MAIN_SEC][MAIN_RTRY].c_str());

		// 如果配置文件中包含调试选项，则将调试选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_BACH))
			appConfig.batchnum = std::atol(appConfig.configers[MAIN_SEC][MAIN_BACH].c_str());

		// 如果配置文件中包含调试选项，则将调试选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_THRD))
			appConfig.threadnum = std::atol(appConfig.configers[MAIN_SEC][MAIN_THRD].c_str());

		// 如果配置文件中包含用户选项，则将用户选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_USER))
			appConfig.userfiled = std::atol(appConfig.configers[MAIN_SEC][MAIN_USER].c_str());

		// 如果配置文件中包含功能选项，则将功能选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_FUNC))
			appConfig.funcfiled = std::atol(appConfig.configers[MAIN_SEC][MAIN_FUNC].c_str());

		// 如果配置文件中包含股票代码，则将功能选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_STKC))
			appConfig.stkcfiled = std::atol(appConfig.configers[MAIN_SEC][MAIN_STKC].c_str());

		// 如果配置文件中包含买卖类别，则将功能选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_TYPE))
			appConfig.typefiled = std::atol(appConfig.configers[MAIN_SEC][MAIN_TYPE].c_str());

		// 如果配置文件中包含代码选项，则将代码选项的值转换为长整型并设置
		if (appConfig.configers[MAIN_SEC].contains(MAIN_CODE))
			appConfig.returnfiled = std::atol(appConfig.configers[MAIN_SEC][MAIN_CODE].c_str());

		char timeBuffer[64] = { 0 };
		std::time_t now = std::time(nullptr);
		std::strftime(timeBuffer, sizeof(timeBuffer), "%Y%m%d%H%M%S", std::localtime(&now));
		appConfig.filename.append("result_log_").append(timeBuffer).append(".txt");
		if (appConfig.threadnum == 0) appConfig.threadnum = std::thread::hardware_concurrency();

		std::string token;
		std::istringstream sslbm(appConfig.configers[KDGW_MID][KDGW_LBM]);
		while (std::getline(sslbm, token, ',') && !token.empty()) {
			appConfig.funclbm[token] = 0;
		}

		std::istringstream ssmkt(appConfig.configers[KDGW_MID][KDGW_MKT]);
		while (std::getline(ssmkt, token, ',') && !token.empty()) {
			appConfig.markets[token] = 0;
		}
	}

	std::string get_last_midfile()
	{
		std::string fileName;
		auto lastFile = getLastProcessedFile();
		if (lastFile) {
			std::cout << "上次处理的文件为：" << lastFile.value() << std::endl;
			std::cout << "是否继续使用该文件？(y/n): ";
			std::string choice;
			std::getline(std::cin, choice); // 使用getline读取整行输入，包括空格

			// 如果用户只按了Enter（即输入为空）或输入了空格、'y'、'Y'，则视为同意
			if (choice[0] == 'x' || choice[0] == 'X') {
				while (true)
				{
					std::cout << "请输入待处理的工作密钥:" << std::endl;
					std::getline(std::cin, fileName);

					char szWorkKey[64] = { 0 };
					CBlowFish oBlowFish((unsigned char*)"SZKINGDOM", 9);
					oBlowFish.Decrypt((const unsigned char*)fileName.data(), (unsigned char*)szWorkKey, fileName.length());
					std::cout << "工作密钥明文：" << szWorkKey << std::endl;
				}
				return "";
			}
			else if (choice.empty() || choice == " " || choice[0] == 'y' || choice[0] == 'Y') {
				fileName = lastFile.value();
			}
			else {
				std::cout << "请输入待处理的mid文件路径，或直接拖拽文件至此:" << std::endl;
				std::getline(std::cin, fileName); // 保持一致性，使用getline读取文件路径
			}
		}
		else {
			std::cout << "未找到上次处理的文件记录。" << std::endl;
			std::cout << "请输入待处理的mid文件路径，或直接拖拽文件至此:" << std::endl;
			//std::cin >> fileName;
			std::getline(std::cin, fileName);
		}

		// 在处理文件之前，先保存这个即将被处理的文件路径
		saveLastProcessedFile(fileName);

		return fileName;
	}

	char* strchr(char* src, int c, int num)
	{
		char* dest;
		dest = src;
		while (*dest++ != 0 && num > 0)
		{
			if (*dest == c)
				num--;
		}
		return  dest;
	}

	char* GetValue(const char* src, char* dest, int num, char ch)
	{
		char* s0 = dest;
		dest[0] = 0;
		while (*src != 0 && *src != '\n' && num != 1)
		{
			if (*src == ch)
				num--;
			src++;
		}
		while (*src != 0 && *src != '\n' && *src != ch)
			*s0++ = *src++;
		*s0 = 0;
		return dest;
	}

	char* GetValue(const char* src, char* dest, int num, char ch, int count)
	{
		/*
			char *s0=dest;
			dest[0]=0;
			while (*src!=0 && *src!='\n' && num!=1)
			{
				if (*src==ch)
					num--;
				src++;
			}
			int icount =0;
			while (*src!=0 && *src!='\n' && *src!=ch && icount++<count)
				*s0++=*src++;
			*s0=0;
			return dest;
		*/
		char* s0 = dest;
		dest[0] = 0;
		while (*src != 0 && num != 1)
		{
			if (*src == ch)
				num--;
			src++;
		}
		int icount = 0;
		while (*src != 0 && *src != ch && icount++ < count)
			*s0++ = *src++;
		*s0 = 0;
		return dest;
	}

	char* ltrim(char* str)
	{
		char* s = str;
		while (*s == ' ' && *s != 0) s++;
		if (s != str)
		{
			strcpy(str, s);
		}
		return str;
	}

	char* rtrim(char* str)
	{
		char* s = str + strlen(str) - 1;
		while (s - str > 0 && (*s == ' ' || *s == '\n')) s--;
		s++;
		*s = 0;
		return str;
	}

	char* alltrim(char* str)
	{
		ltrim(str);
		rtrim(str);
		return str;
	}

	char* padl(char* str, char ch, int num)
	{
		char sBuffer[255];
		memset(sBuffer, ch, num);
		memcpy(sBuffer + num - min(num, (int)strlen(str)), str, min(num, (int)strlen(str)));
		sBuffer[num] = 0;
		strcpy(str, sBuffer);
		return str;
	}

	char* padr(char* str, char ch, int num) {
		char sBuffer[255];
		memset(sBuffer, ch, num);
		memcpy(sBuffer, str, min(num, (int)strlen(str)));
		sBuffer[num] = 0;
		strcpy(str, sBuffer);
		return str;
	}

	char* padc(char* str, char ch, int num)
	{
		char sBuffer[255];
		memset(sBuffer, ch, num);
		memcpy(sBuffer + num - min(num, (int)strlen(str)) / 2, str, min(num, (int)strlen(str)));
		sBuffer[num] = 0;
		strcpy(str, sBuffer);
		return str;
	}

	inline unsigned char btoh(char ch)
	{
		return  ((ch) >= 0x30 && (ch) <= 0x39) ? (ch)-0x30 :
			((ch) >= 0x41 && (ch) <= 0x5a) ? (ch)-0x41 + 0xa :
			((ch) >= 0x61 && (ch) <= 0x7a) ? (ch)-0x61 + 0xa : 0;
	}

	long ahtol(char* str)
	{
		size_t i = strlen(str);
		long lval = 0;

		while (*str)
		{
			lval += ((long)(btoh(*str++))) << (--i << 2);
		}

		return lval;
	}

}

void AtsTimeStat::print()
{
	if (!m_printFlag)
	{
		auto printTime = std::chrono::high_resolution_clock::now();
		std::chrono::duration<double, std::milli> ms_double = printTime - m_axTimer.getbegin();

		auto duration = std::chrono::duration_cast<std::chrono::microseconds>(ms_double);
		auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(ms_double);
		auto s = std::chrono::duration_cast<std::chrono::seconds>(ms_double);
		auto m = std::chrono::duration_cast<std::chrono::minutes>(ms_double);
		auto h = std::chrono::duration_cast<std::chrono::hours>(ms_double);

		std::cout << std::setfill('0') << std::setw(2) << h.count() << "时"
			<< std::setfill('0') << std::setw(2) << (m.count() % 60) << "分"
			<< std::setfill('0') << std::setw(2) << (s.count() % 60) << "秒"
			<< std::setfill('0') << std::setw(2) << (ms.count() % 1000) << "ms"
			<< std::setfill('0') << std::setw(3) << (duration.count() % 1000) << std::endl;
	}

	m_printFlag = true;
}

void AtsTimeStat::print(const std::string& keyword)
{
	auto printTime = std::chrono::high_resolution_clock::now();
	std::chrono::duration<double, std::milli> ms_double = printTime - m_axTimer.getbegin();

	auto duration = std::chrono::duration_cast<std::chrono::microseconds>(ms_double);
	auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(ms_double);
	auto s = std::chrono::duration_cast<std::chrono::seconds>(ms_double);
	auto m = std::chrono::duration_cast<std::chrono::minutes>(ms_double);
	auto h = std::chrono::duration_cast<std::chrono::hours>(ms_double);

	std::cout << keyword << "take " << std::setfill('0') << std::setw(2) << h.count() << "时"
		<< std::setfill('0') << std::setw(2) << (m.count() % 60) << "分"
		<< std::setfill('0') << std::setw(2) << (s.count() % 60) << "秒"
		<< std::setfill('0') << std::setw(2) << (ms.count() % 1000) << "ms"
		<< std::setfill('0') << std::setw(3) << (duration.count() % 1000) << std::endl;

	m_printFlag = true;
}

void ConsistentHash::addNode(size_t nodeId) {
	for (int i = 0; i < virtualNodeCountPerNode; ++i) {
		std::string nodeIdStr = std::to_string(nodeId) + std::to_string(i);
		size_t hashValue = hasher(nodeIdStr);
		virtualNodes.emplace_back(NodeHash{ nodeId, hashValue });
	}
	rebuildVirtualNodes();
}

void ConsistentHash::removeNode(size_t nodeId) {
	removeVirtualNodesForNodeId(nodeId);
	rebuildVirtualNodes();
}

size_t ConsistentHash::getNodeFor(const std::string& key) {
	size_t hashKey = hasher(key);
	auto it = std::lower_bound(virtualNodes.begin(), virtualNodes.end(), hashKey,
		[](const NodeHash& nh, size_t hash) { return nh.hashValue < hash; });
	if (it == virtualNodes.end()) {
		// 如果找不到比hashKey大的节点，就返回第一个节点的nodeId
		return virtualNodes.front().nodeId;
	}
	return it->nodeId;

	// 注意：这里不需要NodeHash对象来比较，因为std::lower_bound期望的第三个参数是一个可调用对象
	// 它接受一个NodeHash和hashKey的类型，并返回一个bool值
}