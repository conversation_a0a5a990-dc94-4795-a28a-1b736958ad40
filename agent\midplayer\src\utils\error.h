﻿#ifndef __CERROR_H__
#define __CERROR_H__
#pragma once

#include "AxError.h"
#include <string>

#define ERRMSGLEN  1024

//---------------------------------------------------------------------------
struct tagErrorMessageList
{
	unsigned int dwCode;
	const char *pszMessage;
};

//---------------------------------------------------------------------------
// 兼容性支持 - 保持原有CError类接口，同时支持新的错误处理系统
class CError  ///错误处理基类,所有要处理错的类都必须继承此类
{
private:
protected:
	unsigned int m_dwErrorCode; ///<错误代码
	char m_szErrorMessage[ERRMSGLEN];
	MidPlayerError m_midPlayerError; ///<新的错误代码
	std::string m_errorContext; ///<错误上下文

public:
  CError();
  ~CError();

  // 原有接口 - 保持兼容性
  char *GetLastErrorMessage();     ///得到错误信息
  unsigned int GetLastErrorCode();        ///得到错误代码
  void SetLastError( unsigned int p_dwErrorCode, const char *p_pszErrorMessageFMT, ...); ///<设置最后的错误
  const char *QueryErrorMessage(unsigned int dwCode);///查询错误代码对应的错误信息

  // 新的错误处理接口
  void SetLastError(MidPlayerError errorCode, const std::string& context = "");
  MidPlayerError GetLastMidPlayerError() const;
  std::string GetFormattedErrorMessage() const;
  void ClearError();
};

#endif
