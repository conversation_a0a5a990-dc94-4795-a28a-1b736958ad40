﻿#ifndef __MIDPLAYER_LOGGER_H__
#define __MIDPLAYER_LOGGER_H__
#pragma once
#include <sstream>
#include <chrono>
#include <mutex>
#include <atomic>
#include "AxConfig.h"
#include "spdlog/spdlog.h"
#include "spdlog/fmt/fmt.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/daily_file_sink.h"


// 统一的日志管理器类
class MidPlayerLogger {
public:
	// 获取单例实例
	static MidPlayerLogger& getInstance();

	// 初始化日志系统
	void initialize();
	void initialize(const MidPlayerConfig::LoggingConfig& config);

	// 获取logger实例
	std::shared_ptr<spdlog::logger> getLogger() const;

	// 设置日志级别
	void setLogLevel(const std::string& level);
	void setLogLevel(spdlog::level::level_enum level);

	// 日志级别转换
	static spdlog::level::level_enum stringToLevel(const std::string& level);
	static std::string levelToString(spdlog::level::level_enum level);

	// 关闭日志系统
	void shutdown();

private:
	MidPlayerLogger() = default;
	~MidPlayerLogger();
	MidPlayerLogger(const MidPlayerLogger&) = delete;
	MidPlayerLogger& operator=(const MidPlayerLogger&) = delete;

	void createLogger(const MidPlayerConfig::LoggingConfig& config);

	std::shared_ptr<spdlog::logger> logger_;
	std::atomic<bool> initialized_{false};
	mutable std::once_flag init_flag_;
};

// 全局logger获取函数
inline std::shared_ptr<spdlog::logger> getLogger() {
	return MidPlayerLogger::getInstance().getLogger();
}

// 日志宏定义
#define MIDLOG_TRACE(msg, ...)    do { auto logger = getLogger(); if (logger) logger->trace(msg, ##__VA_ARGS__); } while(0)
#define MIDLOG_DEBUG(msg, ...)    do { auto logger = getLogger(); if (logger) logger->debug(msg, ##__VA_ARGS__); } while(0)
#define MIDLOG_INFO(msg, ...)     do { auto logger = getLogger(); if (logger) logger->info(msg, ##__VA_ARGS__); } while(0)
#define MIDLOG_WARN(msg, ...)     do { auto logger = getLogger(); if (logger) logger->warn(msg, ##__VA_ARGS__); } while(0)
#define MIDLOG_ERROR(msg, ...)    do { auto logger = getLogger(); if (logger) logger->error(msg, ##__VA_ARGS__); } while(0)
#define MIDLOG_CRITICAL(msg, ...) do { auto logger = getLogger(); if (logger) logger->critical(msg, ##__VA_ARGS__); } while(0)

// 格式化版本
#define LOG4CXX_TRACE_FMT(logger, fmt, ...)    MIDLOG_TRACE(fmt, ##__VA_ARGS__)
#define LOG4CXX_DEBUG_FMT(logger, fmt, ...)    MIDLOG_DEBUG(fmt, ##__VA_ARGS__)
#define LOG4CXX_INFO_FMT(logger, fmt, ...)     MIDLOG_INFO(fmt, ##__VA_ARGS__)
#define LOG4CXX_WARN_FMT(logger, fmt, ...)     MIDLOG_WARN(fmt, ##__VA_ARGS__)
#define LOG4CXX_ERROR_FMT(logger, fmt, ...)    MIDLOG_ERROR(fmt, ##__VA_ARGS__)
#define LOG4CXX_FATAL_FMT(logger, fmt, ...)    MIDLOG_CRITICAL(fmt, ##__VA_ARGS__)

// 便捷宏定义
#define MFLOG_FATAL(fmt, ...)    LOG4CXX_FATAL_FMT(nullptr, fmt, ##__VA_ARGS__)
#define MFLOG_ERROR(fmt, ...)   LOG4CXX_ERROR_FMT(nullptr, fmt, ##__VA_ARGS__)
#define MFLOG_WARN(fmt, ...)    LOG4CXX_WARN_FMT(nullptr, fmt, ##__VA_ARGS__)
#define MFLOG_INFO(fmt, ...)     LOG4CXX_INFO_FMT(nullptr, fmt, ##__VA_ARGS__)
#define MFLOG_DEBUG(fmt, ...)   LOG4CXX_DEBUG_FMT(nullptr, fmt, ##__VA_ARGS__)
#define MFLOG_TRACE(fmt, ...)   LOG4CXX_TRACE_FMT(nullptr, fmt, ##__VA_ARGS__)
#define MFLOG_DETAIL(fmt, ...)  LOG4CXX_TRACE_FMT(nullptr, fmt, ##__VA_ARGS__)

// 兼容LOG4CXX宏
#define LOG4CXX_TRACE(logger, msg) do { \
    auto log_ptr = getLogger(); \
    if (log_ptr) { \
        std::ostringstream oss; \
        oss << msg; \
        log_ptr->trace(oss.str()); \
    } \
} while(0)

#define LOG4CXX_DEBUG(logger, msg) do { \
    auto log_ptr = getLogger(); \
    if (log_ptr) { \
        std::ostringstream oss; \
        oss << msg; \
        log_ptr->debug(oss.str()); \
    } \
} while(0)

#define LOG4CXX_INFO(logger, msg) do { \
    auto log_ptr = getLogger(); \
    if (log_ptr) { \
        std::ostringstream oss; \
        oss << msg; \
        log_ptr->info(oss.str()); \
    } \
} while(0)

#define LOG4CXX_WARN(logger, msg) do { \
    auto log_ptr = getLogger(); \
    if (log_ptr) { \
        std::ostringstream oss; \
        oss << msg; \
        log_ptr->warn(oss.str()); \
    } \
} while(0)

#define LOG4CXX_ERROR(logger, msg) do { \
    auto log_ptr = getLogger(); \
    if (log_ptr) { \
        std::ostringstream oss; \
        oss << msg; \
        log_ptr->error(oss.str()); \
    } \
} while(0)

#define LOG4CXX_FATAL(logger, msg) do { \
    auto log_ptr = getLogger(); \
    if (log_ptr) { \
        std::ostringstream oss; \
        oss << msg; \
        log_ptr->critical(oss.str()); \
    } \
} while(0)

// 条件日志宏
#define MIDLOG_IF(condition, level, msg, ...) \
    do { \
        if (condition) { \
            MIDLOG_##level(msg, ##__VA_ARGS__); \
        } \
    } while(0)

// 性能日志宏
#define MIDLOG_PERF_START(name) \
    auto perf_start_##name = std::chrono::high_resolution_clock::now()

#define MIDLOG_PERF_END(name, msg, ...) \
    do { \
        auto perf_end_##name = std::chrono::high_resolution_clock::now(); \
        auto perf_duration_##name = std::chrono::duration_cast<std::chrono::microseconds>(perf_end_##name - perf_start_##name); \
        MIDLOG_DEBUG("PERF [{}]: {} ({}μs)", #name, fmt::format(msg, ##__VA_ARGS__), perf_duration_##name.count()); \
    } while(0)

// 错误日志宏
#define MIDLOG_ERROR_CODE(error_code, context, ...) \
    do { \
        auto& errorMgr = MidPlayerErrorManager::getInstance(); \
        std::string formatted_msg = errorMgr.formatError(error_code, fmt::format(context, ##__VA_ARGS__)); \
        MIDLOG_ERROR(formatted_msg); \
    } while(0)

#endif // __MIDPLAYER_LOGGER_H__
