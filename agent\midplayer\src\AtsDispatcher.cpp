﻿#include "stdafx.h"
//#include "AtsDispatcher.h"
//using namespace progschj;
//
//int test()
//{
//	ThreadPool pool;
//	std::vector< std::future<int> > results;
//
//	for (int i = 0; i < 8; ++i) {
//		results.emplace_back(
//			pool.enqueue([i] {
//				std::cout << "hello " << i << std::endl;
//				std::this_thread::sleep_for(std::chrono::seconds(1));
//				std::cout << "world " << i << std::endl;
//				return i * i;
//				})
//		);
//	}
//
//	pool.wait_until_empty();
//	pool.wait_until_nothing_in_flight();
//
//	for (auto&& result : results)
//		std::cout << result.get() << ' ';
//	std::cout << std::endl;
//
//	return 0;
//}

//std::hash<std::string> hasher;
//std::vector<std::vector<tagMidRequst>> heaps(100);
//
//for (const auto& message : messages) {
//	std::string customerNumber = getCustomerNumber(message);
//	size_t hashValue;
//	if (!customerNumber.empty()) {
//		hashValue = hasher(customerNumber) % 80 + 20; // 用户堆
//	}
//	else {
//		hashValue = hasher(std::to_string(0)) % 20; // 公共堆
//	}
//	heaps[hashValue].push_back(message);
//}