﻿#ifndef __MIDPLAYER_CONFIG_H__
#define __MIDPLAYER_CONFIG_H__
#pragma once
#include "AxError.h"


// 配置段枚举
enum class ConfigSection : int {
	MAIN,
	MID_CFG,
	KDG_STD,
	KDG_TDX,
	LOGGING,
	PERFORMANCE,
	NETWORK
};

// 配置管理单例类
class MidPlayerConfig {
public:
	// 获取单例实例
	static MidPlayerConfig& getInstance();

	// 配置文件操作
	MidPlayerResult loadFromFile(const std::string& filename);
	MidPlayerResult saveToFile(const std::string& filename) const;
	MidPlayerResult reloadConfig();

	// 通用配置获取方法
	template<typename T>
	T getValue(ConfigSection section, const std::string& key, const T& defaultValue = T{}) const;

	template<typename T>
	MidPlayerResult setValue(ConfigSection section, const std::string& key, const T& value);

	// 便捷的配置获取方法
	std::string getString(ConfigSection section, const std::string& key, const std::string& defaultValue = "") const;
	int getInt(ConfigSection section, const std::string& key, int defaultValue = 0) const;
	bool getBool(ConfigSection section, const std::string& key, bool defaultValue = false) const;
	double getDouble(ConfigSection section, const std::string& key, double defaultValue = 0.0) const;

	// 特定配置获取方法
	struct MainConfig {
		bool readOnly = false;
		bool stgEnable = false;
		bool utilMatch = false;
		std::string beginMid = "ipdst";
		std::string memchrAlgo = "find";
		int debugLevel = 1;
		int batchNum = 51200;
		int threadNum = 0;  // 0表示自动检测
		int retryCount = 8;
		int userField = 5;
		int funcField = 13;
		int stkcField = 19;
		int typeField = 20;
		int codeField = 5;
	};

	struct MidCfgConfig {
		std::string reqFlag = "REQ:";
		std::string ansFlag = "RESP:";
		std::string skipFunc = "90,100,301,368,1760";
		std::string skipMarket = "20,30";
	};

	struct GatewayConfig {
		std::string server = "127.0.0.1";
		int port = 9100;
		int timeout = 45;
		std::string checkin = "0000|0000|CRCCRCCR|KDGATEWAY1.2|9988|127.0.0.1|999|4|SS|2|";
		std::string version = "KDGATEWAY1.2";
		std::string crcCode = "12345678";
		std::string workKey = "12345678";
	};

	struct LoggingConfig {
		std::string logLevel = "INFO";
		std::string logFile = "logs/midplayer.log";
		std::string rotationType = "daily";
		int rotationHour = 0;
		int rotationMinute = 0;
		int maxFileSize = 10485760;  // 10MB
		int maxFiles = 30;
		bool consoleOutput = true;
		bool fileOutput = true;
		bool singleThread = true;
		std::string consolePattern = "%Y-%m-%d %H:%M:%S.%e [%t] [%^%l%$] %n - %v";
		std::string filePattern = "%Y-%m-%d %H:%M:%S.%e [%t] [%l] %n - %v";
	};

	struct PerformanceConfig {
		int mmapBufferSize = 67108864;  // 64MB
		int sendBufferSize = 65536;     // 64KB
		int recvBufferSize = 65536;     // 64KB
		int messageBatchSize = 1000;
		int statsUpdateInterval = 1000;
		int progressUpdateInterval = 500;
	};

	struct NetworkConfig {
		int connectTimeout = 30;
		int sendTimeout = 10;
		int recvTimeout = 10;
		int heartbeatInterval = 30;
		int reconnectInterval = 5;
		int maxReconnectAttempts = 3;
	};

	// 获取配置结构体
	MainConfig getMainConfig() const;
	MidCfgConfig getMidCfgConfig() const;
	GatewayConfig getStdGatewayConfig() const;
	GatewayConfig getTdxGatewayConfig() const;
	LoggingConfig getLoggingConfig() const;
	PerformanceConfig getPerformanceConfig() const;
	NetworkConfig getNetworkConfig() const;

	// 配置验证
	MidPlayerResult validateConfig() const;

	// 配置监听器
	using ConfigChangeCallback = std::function<void(ConfigSection, const std::string&, const std::string&)>;
	void addConfigChangeListener(ConfigChangeCallback callback);

private:
	MidPlayerConfig() = default;
	~MidPlayerConfig() = default;
	MidPlayerConfig(const MidPlayerConfig&) = delete;
	MidPlayerConfig& operator=(const MidPlayerConfig&) = delete;

	// 内部方法
	std::string sectionToString(ConfigSection section) const;
	ConfigSection stringToSection(const std::string& sectionName) const;
	MidPlayerResult parseConfigFile(const std::string& filename);
	void notifyConfigChange(ConfigSection section, const std::string& key, const std::string& value);

	// 数据成员
	mutable std::mutex configMutex_;
	std::string configFilename_;
	std::unordered_map<std::string, std::unordered_map<std::string, std::string>> configData_;
	std::vector<ConfigChangeCallback> changeListeners_;
};

// 模板方法实现
template<typename T>
T MidPlayerConfig::getValue(ConfigSection section, const std::string& key, const T& defaultValue) const {
	std::lock_guard<std::mutex> lock(configMutex_);

	std::string sectionName = sectionToString(section);
	auto sectionIt = configData_.find(sectionName);
	if (sectionIt == configData_.end()) {
		return defaultValue;
	}

	auto keyIt = sectionIt->second.find(key);
	if (keyIt == sectionIt->second.end()) {
		return defaultValue;
	}

	// 类型转换逻辑
	const std::string& value = keyIt->second;
	if constexpr (std::is_same_v<T, std::string>) {
		return value;
	}
	else if constexpr (std::is_same_v<T, int>) {
		return std::stoi(value);
	}
	else if constexpr (std::is_same_v<T, bool>) {
		return (value == "true" || value == "1" || value == "yes");
	}
	else if constexpr (std::is_same_v<T, double>) {
		return std::stod(value);
	}
	else {
		return defaultValue;
	}
}

template<typename T>
MidPlayerResult MidPlayerConfig::setValue(ConfigSection section, const std::string& key, const T& value) {
	std::lock_guard<std::mutex> lock(configMutex_);

	std::string sectionName = sectionToString(section);
	std::string valueStr;

	if constexpr (std::is_same_v<T, std::string>) {
		valueStr = value;
	}
	else if constexpr (std::is_same_v<T, bool>) {
		valueStr = value ? "true" : "false";
	}
	else {
		valueStr = std::to_string(value);
	}

	configData_[sectionName][key] = valueStr;
	notifyConfigChange(section, key, valueStr);

	return MIDPLAYER_SUCCESS();
}

#endif // __MIDPLAYER_CONFIG_H__
