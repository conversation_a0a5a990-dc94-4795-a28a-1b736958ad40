﻿#ifndef _AX_PLATFORM_H__
#define _AX_PLATFORM_H__
#pragma once
#include <set>
#include <map>
#include <chrono>
#include <string>
#include <memory>
#include <stdio.h>
#include <string>
#include <thread>
#include <vector>
#include <iostream>
///////////////////////////////////////////////////////////////
#include <string>
#include <algorithm>
#include <cmath>
#include <time.h>
#include <sstream>
#include <stdio.h>
#include <assert.h>
#include <stdint.h>
///////////////////////////////////////////////////////////////
#include "spdlog/fmt/fmt.h"

#define AX_PLATFORM_WIN 1
#define AX_PLATFORM_LINUX 2

#define AX_COMPILER_MSVC 1
#define AX_COMPILER_GNUC 2

#define AX_ENDIAN_LITTLE 1
#define AX_ENDIAN_BIG 2

#define AX_ARCHITECTURE_32 1
#define AX_ARCHITECTURE_64 2

// 检查编译器
#if defined( _MSC_VER )
#   define AX_COMPILER AX_COMPILER_MSVC
#   define AX_COMP_VER _MSC_VER
#elif defined( __GNUC__ )
#   define AX_COMPILER AX_COMPILER_GNUC
#   define AX_COMP_VER (((__GNUC__)*100) + \
                        (__GNUC_MINOR__*10) + \
                        __GNUC_PATCHLEVEL__)
#else
#   pragma error "No known compiler. Abort! Abort!"
#endif

// 检查操作系统
#if defined( __WIN32__ ) || defined( _WIN32 ) || defined(_WINDOWS) || defined(WIN) || defined(_WIN64) || defined( __WIN64__ )
#   define AX_PLATFORM AX_PLATFORM_WIN
//////////////////////////////////////////////////////////////////////////
#else
#   define AX_PLATFORM AX_PLATFORM_LINUX
#endif

// 检查系统位数
#if AX_PLATFORM == AX_PLATFORM_WIN

#if defined(_WIN64) || defined(__WIN64__)
#   define AX_ARCH_TYPE AX_ARCHITECTURE_64
#else
#   define AX_ARCH_TYPE AX_ARCHITECTURE_32
#endif

#elif AX_PLATFORM == AX_PLATFORM_LINUX

#if defined(__x86_64__)
#   define AX_ARCH_TYPE AX_ARCHITECTURE_64
#else
#   define AX_ARCH_TYPE AX_ARCHITECTURE_32
#endif

#endif

#ifndef _MSC_VER
#include <sys/syscall.h>
#include <sys/time.h>
#include <unistd.h>

#define EPOCHFILETIME 11644473600000000ULL
#else
#include <windows.h>
#include <time.h>
#include <process.h>
#define EPOCHFILETIME 11644473600000000Ui64
#endif

#if AX_PLATFORM == AX_PLATFORM_WIN
#define __WORDSIZE 64
#define AXSPRINTF sprintf_s
#define AXSTRICMP stricmp
#define AXGetPID() getpid()
typedef unsigned int AX_THREAD_ID;

inline AX_THREAD_ID ThreadId()
{
	return GetCurrentThreadId();
}

inline struct tm* localtime_r(const time_t* timep, struct tm* result)
{
	localtime_s(result, timep);
	return result;
}

#define strcasecmp   _stricmp
#define strncasecmp  _strnicmp
#else
#define AXSPRINTF snprintf
#define AXSTRICMP strcasecmp

#define AXGetPID() getpid()
typedef unsigned long int AX_THREAD_ID;

inline AX_THREAD_ID ThreadId()
{
	return syscall(SYS_gettid);
}

#endif

#define AXSLEEP(us) std::this_thread::sleep_for(std::chrono::microseconds(us));

//use actor mode--begin
#define AX_ACTOR_THREAD_COUNT 3
#define AX_DEFAULT_MYSQL_DB_NAME "proto_ff"

#ifndef AX_USE_ACTOR
#define AX_USE_ACTOR
#endif

#ifdef AX_USE_ACTOR

#ifdef AX_DEBUG_MODE
#define THERON_DEBUG 1
#else
#define THERON_DEBUG 0
#endif

#ifndef THERON_CPP11
#define THERON_CPP11 1
#endif

#ifndef AX_ENABLE_SSL
#define AX_ENABLE_SSL 1
#endif

#ifndef USE_NET_EVPP
#define USE_NET_EVPP
#endif

#endif
//use actor mode--end

#define GET_CLASS_NAME(className) (#className)

#define AX_SHARE_PTR std::shared_ptr

#define AX_NEW new

#define  AX_SAFE_DELETE(pData) { try { delete pData; } catch (...) { AX_COMM_ASSERT_MSG(false, "AXSafeDelete error"); } pData=NULL; }

inline int64_t AXGetTime()
{
	return std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
}

inline int64_t AXGetSecondTime()
{
	return std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
}

inline int64_t AXGetMicroSecondTime()
{
	return std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
}

inline int64_t AXGetNanoSeccondTime()
{
	return std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
}

//
#ifndef AX_CASE_STRING_BIGIN
#define AX_CASE_STRING_BIGIN(state) switch(state){
#define AX_CASE_STRING(state) case state:return #state;break;
#define AX_CASE_STRING_END()  default:return "Unknown";break;}
#endif

#ifndef AX_FUNCTION_LINE
#define AX_FUNCTION_LINE __FUNCTION__, __LINE__
#endif

#ifndef AX_FILE_FUNCTION_LINE
#define AX_FILE_FUNCTION_LINE __FILE__, __FUNCTION__, __LINE__
#endif
#define AX_FORMAT(my_fmt, ...)             fmt::format(my_fmt, ##__VA_ARGS__)
#define AX_FORMAT_EXPR(str, my_fmt, ...)   try { str = fmt::format(my_fmt, ##__VA_ARGS__); } catch (fmt::v5::format_error& error) { AXLogError(AX_LOG_SYSTEMLOG, 0, "fmt:{} err:{}{}", my_fmt, error.what()); }

template<typename... ARGS>
inline std::string AXFormatFunc(const char* my_fmt, const ARGS& ... args)
{
	try {
		std::string str = fmt::format(my_fmt, args...);
		return str;
	}
	catch (typename fmt::v10::format_error& error)
	{
		return my_fmt + std::string(" err:") + error.what();
	}
}

#define AX_FORMAT_FUNC(my_fmt, ...)             AXFormatFunc(my_fmt, ##__VA_ARGS__)

#define MMO_LOWORD(l)           ((uint16_t)(l))
#define MMO_HIWORD(l)           ((uint16_t)(((uint32_t)(l) >> 16) & 0xFFFF))
#define MMO_LOBYTE(w)           ((uint8_t)(w))
#define MMO_HIBYTE(w)           ((uint8_t)(((uint16_t)(w) >> 8) & 0xFF))
#define MMO_LOWLONG(ll)         ((uint32_t)(ll))
#define MMO_HILONG(ll)           (uint32_t)(((uint64_t)(ll) >> 32) & 0xFFFFFFFF)
#define MMO_MAKEWORD(a, b)      ((uint16_t)(((uint8_t)(a))  | ((uint16_t)((uint8_t)(b))) << 8))
#define MMO_MAKELONG(a, b)      ((uint32_t)(((uint16_t)(a)) | ((uint32_t)((uint16_t)(b))) << 16))
#define MMO_MAKELONGLONG(a, b)  ((uint64_t)(((uint32_t)(a)) | ((uint64_t)((uint32_t)(b))) << 32))

#define MAKE_UINT32(low, high)    ((uint32_t)(((uint16_t)((uint32_t)(low) & 0xffff)) | ((uint32_t)((uint16_t)((uint32_t)(high) & 0xffff))) << 16))
#define HIGH_UINT16(l) ((uint16_t)((uint32_t)(l) >> 16))
#define LOW_UINT16(l) ((uint16_t)((uint32_t)(l) & 0xffff))

#define MAKE_UINT64(low, high)    ((uint64_t)(((uint32_t)((uint64_t)(low) & 0xffffffff)) | ((uint64_t)((uint32_t)((uint64_t)(high) & 0xffffffff))) << 32))
#define HIGH_UINT32(l) ((uint32_t)((uint64_t)(l) >> 32))
#define LOW_UINT32(l) ((uint32_t)((uint64_t)(l) & 0xffffffff))

#define BIT_ENABLED(AWORD, BITS) (((AWORD) & (BITS)) == (BITS))
#define BIT_DISABLED(AWORD, BITS) (((AWORD) & (BITS)) == 0)
#define SET_BITS(AWORD, BITS) ((AWORD) |= (BITS))
#define CLR_BITS(AWORD, BITS) ((AWORD) &= ~(BITS))

template<typename T, size_t N>
char(&ArraySizeHelper(T(&array)[N]))[N];


#ifndef _MSC_VER

template<typename T, size_t N>
char(&ArraySizeHelper(const T(&array)[N]))[N];

#endif


#ifndef _MSC_VER
#define ARRAYSIZE(array) (sizeof(ArraySizeHelper(array)))
#endif

#define ARRAYSIZE_UNSAFE(a) \
    ((sizeof(a) / sizeof(*(a))) / \
     static_cast<size_t>(!(sizeof(a) % sizeof(*(a)))))

#ifndef AX_DEBUG_MODE
#define TEST_SERVER
#endif

#ifndef AX_MACRO_FUNCTION
#if AX_PLATFORM == AX_PLATFORM_WIN
//#define AX_MACRO_FUNCTION __FUNCSIG__
#define AX_MACRO_FUNCTION __FUNCTION__
#else
//#define AX_MACRO_FUNCTION __PRETTY_FUNCTION__
#define AX_MACRO_FUNCTION __FUNCTION__
#endif
#endif

//时间轴检查频率 ms
#define TIMER_AXIS_CHECK_FREQUENCE 32
//时间轴刻度
#define TIME_GRID 64
//时间轴长度
#define TIME_AXIS_LENGTH            120000        // 毫秒为单位的
#define TIME_AXIS_SECLENGTH       108000        // 秒为单位的支持到30个小时
#define INVALID_TIMER                0xffffffff  // 无效定时器
#define IAXINITY_CALL                0xffffffff  // 调用无限次

#endif