﻿#ifndef __MINI_SOCKET__
#define __MINI_SOCKET__
#pragma once
#include <winsock.h>
#include <wsipx.h>
#include <wsnwlink.h>

class CMiniSocket
{
public:
	CMiniSocket();
	virtual ~CMiniSocket();

public:
	static int initWinSocket();
	int connectToServer(const char* address, int port, int timeout);
	int reconnect(int timeout);
	int closeSocket();

	int clearBuffer(int waitTime = 0);
	int receiveLength(int waitTime = 0);
	int waitReceive(int waitTime);

	int receiveBuffer(void* buffer, int bufferSize);
	int sendBuffer(const void* buffer, int bufferSize);

	const char* getErrorMessage() const;
	void setErrorMessage(const char* errorMessage);

	int setReadTimeout(int timeout);
	int setWriteTimeout(int timeout);
	int setNonBlockingMode(bool mode);

	int waitReply(int timeout);
	SOCKET getSocket() const;

private:
	void logErrorMessage(const char* errinfo);

	unsigned char binaryToHex(char ch);
	void asciiToHex(char* dest, const char* source, int count);

private:
	SOCKET socketHandle = INVALID_SOCKET;
	int socketTimeout = 20; // 超时时间，秒为单位

	char* errorMessage = nullptr;

	bool isConnected = false;
	char serverAddress[32] = "";
	int serverPort = 0;
};

#endif





















