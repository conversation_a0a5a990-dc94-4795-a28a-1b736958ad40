﻿#ifndef __AX_ERROR_H__
#define __AX_ERROR_H__
#pragma once

#include <string>
#include <unordered_map>
#include <stdexcept>
#include <functional>

// 统一错误码枚举类
enum class MidPlayerError : int {
	// 成功状态
	SUCCESS = 0,

	// 文件操作错误 (1000-1999)
	FILE_NOT_FOUND = 1001,
	FILE_OPEN_FAILED = 1002,
	FILE_READ_FAILED = 1003,
	FILE_WRITE_FAILED = 1004,
	FILE_MAPPING_FAILED = 1005,
	FILE_SIZE_ERROR = 1006,

	// 网络通信错误 (2000-2999)
	NETWORK_CONNECTION_FAILED = 2001,
	NETWORK_SEND_FAILED = 2002,
	NETWORK_RECEIVE_FAILED = 2003,
	NETWORK_TIMEOUT = 2004,
	NETWORK_SOCKET_ERROR = 2005,
	NETWORK_DISCONNECT = 2006,

	// 协议解析错误 (3000-3999)
	PROTOCOL_INVALID_MESSAGE = 3001,
	PROTOCOL_CRC_ERROR = 3002,
	PROTOCOL_VERSION_MISMATCH = 3003,
	PROTOCOL_FIELD_EXTRACT_FAILED = 3004,
	PROTOCOL_MESSAGE_TOO_SHORT = 3005,
	PROTOCOL_MESSAGE_INCOMPLETE = 3006,

	// 配置错误 (4000-4999)
	CONFIG_FILE_NOT_FOUND = 4001,
	CONFIG_PARSE_ERROR = 4002,
	CONFIG_INVALID_VALUE = 4003,
	CONFIG_MISSING_SECTION = 4004,
	CONFIG_MISSING_KEY = 4005,

	// 系统资源错误 (5000-5999)
	MEMORY_ALLOCATION_FAILED = 5001,
	THREAD_CREATE_FAILED = 5002,
	MUTEX_LOCK_FAILED = 5003,
	SYSTEM_RESOURCE_EXHAUSTED = 5004,

	// 业务逻辑错误 (6000-6999)
	INVALID_PARAMETER = 6001,
	OPERATION_NOT_SUPPORTED = 6002,
	STATE_ERROR = 6003,
	AUTHENTICATION_FAILED = 6004,
	AUTHORIZATION_FAILED = 6005,

	// 未知错误
	UNKNOWN_ERROR = 9999
};

// 错误信息管理类
class MidPlayerErrorManager {
public:
	static MidPlayerErrorManager& getInstance();

	// 获取错误描述
	std::string getErrorMessage(MidPlayerError errorCode) const;

	// 设置自定义错误消息
	void setCustomErrorMessage(MidPlayerError errorCode, const std::string& message);

	// 格式化错误消息
	std::string formatError(MidPlayerError errorCode, const std::string& context = "") const;

private:
	MidPlayerErrorManager();
	void initializeErrorMessages();

	std::unordered_map<MidPlayerError, std::string> errorMessages_;
};

// 简单的错误结果类，用于void操作
class MidPlayerResult {
public:
	MidPlayerResult() : error_(MidPlayerError::SUCCESS) {}
	MidPlayerResult(MidPlayerError error) : error_(error) {}
	MidPlayerResult(MidPlayerError error, const std::string& message)
		: error_(error), errorMessage_(message) {}

	bool isSuccess() const { return error_ == MidPlayerError::SUCCESS; }
	bool hasError() const { return error_ != MidPlayerError::SUCCESS; }

	MidPlayerError getError() const { return error_; }
	const std::string& getErrorMessage() const { return errorMessage_; }

private:
	MidPlayerError error_;
	std::string errorMessage_;
};

// 便捷宏定义
#define MIDPLAYER_SUCCESS() MidPlayerResult()
#define MIDPLAYER_ERROR(code) MidPlayerResult(code)
#define MIDPLAYER_ERROR_MSG(code, msg) MidPlayerResult(code, msg)

// 错误检查宏
#define MIDPLAYER_CHECK(condition, error_code) \
    do { \
        if (!(condition)) { \
            return MidPlayerResult(error_code); \
        } \
    } while(0)

#define MIDPLAYER_CHECK_MSG(condition, error_code, msg) \
    do { \
        if (!(condition)) { \
            return MidPlayerResult(error_code, msg); \
        } \
    } while(0)

#endif // __AX_ERROR_H__
