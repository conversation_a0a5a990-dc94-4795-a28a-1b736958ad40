﻿/*
* MidMessage是表示MID协议中的消息的类
* 协议描述：
* 第三方网关必须以局域网方式或数据专线方式接入KDMID三方网关.不允许将KDMID端口直接暴露在公共网络上。请求包数据域中不能包含符号‘，’后台BP的LBM处理视‘，’为每一个字段数据的结束符号。
* 第三方网关采用TCP通讯协议与KDMID建立多个连接。每个连接采用同步一问一答方式交互,发送请求数据包后,必须等待应答数据包数据,方可发起下一个请求。
*/
/*
* 协议包逻辑结构概述:
* 1、请求包定义：所有数据报文被定义为一串字符集。在这一字符串集中，各字符串均需以“|”字符为分隔符，每个数据包最后也用“|”结束。如果前台数据字符串中含有'|'字符或中文字符中有0X7C，则转义成'&#124;'发往后台。
* 请求包由两部分组成：请求包头和请求数据；请求包头结构如下：
* 域名---------说明
* 头长度			CAHR(4),前面以字符'0'填充，请求包头长度指“请求包头”的字节长度
* 数据域长度		CHAR(4),前面以字符'0'填充，指“请求数据”的长度
* CRC校验码		CHAR(8), 见注4,7
* 版本号			当前协议版本编号(固定串“KDGATEWAY1.2”)
* 用户代码		CHAR(10), 登陆后送，为操作者代码
* 操作站点		CHAR(64)
* 开户分支		CHAR(6), 登陆后送
* 操作渠道		CHAR(1), 见数据字典
* 会话序号		CHAR(16), 登陆后送
* 保留字段1		CHAR(20), 用户角色 OP_ROLE(2006-4-12) 注9
* 保留字段2		CHAR(20), 请求包序列号    (2006-4-12)
* 保留字段3		CHAR(20), 多柜台转发路由标记，见网关CONFIG.INI的配置
*
* 请求数据结构如下：
* 请求数据=请求功能号(CHAR(5)) + 请求数据域
*
* 2、应答包定义：所有数据报文被定义为一串字符集。在这一字符串集中，各字符串均需以“|”字符为分隔符，每个数据包最后也用“|”结束。如果后台数据字符串中含有'&#124;'则转义成'|'。
* 应答包由两部分组成：应答包头和应答数据；应答包头结构如下：
* 域名---------说明
* 头长度			CHAR(4),前面以字符'0'填充
* 数据长度		CHAR(N),前面以字符'0'填充
* CRC校验码		CHAR(8), 见注5,7
* 版本号			当前协议版本编号(固定串“KDGATEWAY1.2”)
* 返回码			CHAR(10), “0”表示正常
* 返回信息		CHAR(200),返回非0则表示交易处理出现某种交易错误或系统错误(见数据字典)
* 后续包标示		CHAR(1),'0'－无，'1'－表示有后续包(取后续包发99请求)
* 应答字段数		CHAR(10)
* 应答记录数		CHAR(10)
* 原请求功能号	CHAR(20), 2006-4-12增加
* 请求包序列号	CHAR(20), 2006-4-12增加
* 保留字段2		CHAR(20), 2006-4-12增加
*/
#ifndef _MID_MESSAGE_H__
#define _MID_MESSAGE_H__
#pragma once

struct tagMidRequst
{
	// 请求包头相关
	uint32_t m_headerLength;			// 请求包头长度
	uint32_t	m_dataLength;			// 请求数据长度

	std::string m_crcCheckCode;	// CHAR(8), CRC校验码
	std::string m_versionNumber;	// 固定串“KDGATEWAY1.2”
	std::string m_userCode;		// CHAR(10), 操作者代码
	std::string m_operationSite;	// CHAR(64), 操作站点
	std::string m_accountBranch;// CHAR(6), 开户分支
	std::string m_operationChannel;// CHAR(1), 操作渠道
	std::string m_sessionNumber;	// CHAR(16), 会话序号
	std::string m_userRole;			// CHAR(20), 用户角色 OP_ROLE(2006-4-12)
	std::string m_routeFlag;		// CHAR(20), 多柜台转发路由标记，见网关CONFIG.INI的配置
	std::string m_requestSerialN;// 请求包序列号

	// 请求数据
	std::string m_reqFunctionCode;     // CHAR(5), 请求功能号
	std::string requestData;      // 请求数据域
};

struct tagMidAnswer
{
	// 应答包头相关
	uint32_t m_headerLength;		// 请求包头长度
	uint32_t	m_dataLength;		// 请求数据长度

	std::string m_crcCheckCode;	// CHAR(8), CRC校验码
	std::string m_versionNumber;	// 固定串“KDGATEWAY1.2”
	std::string m_responseCode;	// CHAR(10), 返回码
	std::string m_responseMessage;	// CHAR(200), 返回信息
	std::string m_morePacketsFlag;// CHAR(1), 是否有后续包
	std::string m_requestSerialN;// 请求包序列号
	uint32_t m_respFieldCount;// CHAR(10), 应答字段数
	uint32_t m_respRecordCount;// CHAR(10), 应答记录数

	std::string m_oriFunctionCode;// CHAR(20), 原请求功能号
	// 应答数据部分可能较为复杂，根据不同的请求功能号，数据结构可能不同
	// 可以考虑使用一个灵活的数据结构来存储，比如使用std::vector<std::pair<std::string, std::string>>存储键值对
	std::vector<std::pair<std::string, std::string>> responseData;
};

class MidMessage
{
public:
	// 构造函数
	MidMessage() {}
	// 析构函数
	~MidMessage() {}

	// 设置请求包头
	void SetRequestHeader(const tagMidRequst& request);
	// 设置请求数据
	void SetRequestData(const std::string& requestData);
	// 获取请求包
	std::string GetRequestMessage();

	// 设置请求包头的各个字段的值
    void SetRequestHeaderLength(uint32_t length) { m_request.m_headerLength = length; }
    void SetRequestDataLength(uint32_t length) { m_request.m_dataLength = length; }
    void SetRequestSerialNumber(const std::string& serialNumber) { m_request.m_requestSerialN = serialNumber; }
    void SetRequestCRCCheckCode(const std::string& crcCheckCode) { m_request.m_crcCheckCode = crcCheckCode; }
    void SetRequestVersionNumber(const std::string& versionNumber) { m_request.m_versionNumber = versionNumber; }
    void SetRequestUserCode(const std::string& userCode) { m_request.m_userCode = userCode; }
    void SetRequestOperationSite(const std::string& operationSite) { m_request.m_operationSite = operationSite; }
    void SetRequestAccountBranch(const std::string& accountBranch) { m_request.m_accountBranch = accountBranch; }
    void SetRequestOperationChannel(const std::string& operationChannel) { m_request.m_operationChannel = operationChannel; }
    void SetRequestSessionNumber(const std::string& sessionNumber) { m_request.m_sessionNumber = sessionNumber; }
    void SetRequestUserRole(const std::string& userRole) { m_request.m_userRole = userRole; }
    void SetRequestRouteFlag(const std::string& routeFlag) { m_request.m_routeFlag = routeFlag; }
	void SetRequestFunctionCode(const std::string& functionCode) { m_request.m_reqFunctionCode = functionCode; }

	// 设置应答包头
	void SetAnswerHeader(const tagMidAnswer& answer);
	// 设置应答数据
	void SetAnswerData(const std::vector<std::pair<std::string, std::string>>& responseData);
	// 获取应答包
	std::string GetAnswerMessage();

	// 设置应答包头的各个字段的值
    void SetAnswerHeaderLength(uint32_t length) { m_answer.m_headerLength = length; }
    void SetAnswerDataLength(uint32_t length) { m_answer.m_dataLength = length; }
    void SetAnswerCRCCheckCode(const std::string& crcCheckCode) { m_answer.m_crcCheckCode = crcCheckCode; }
    void SetAnswerVersionNumber(const std::string& versionNumber) { m_answer.m_versionNumber = versionNumber; }
    void SetAnswerResponseCode(const std::string& responseCode) { m_answer.m_responseCode = responseCode; }
    void SetAnswerResponseMessage(const std::string& responseMessage) { m_answer.m_responseMessage = responseMessage; }
    void SetAnswerMorePacketsFlag(const std::string& morePacketsFlag) { m_answer.m_morePacketsFlag = morePacketsFlag; }
    void SetAnswerRespFieldCount(uint32_t count) { m_answer.m_respFieldCount = count; }
    void SetAnswerRespRecordCount(uint32_t count) { m_answer.m_respRecordCount = count; }
    void SetAnswerOriFunctionCode(const std::string& oriFunctionCode) { m_answer.m_oriFunctionCode = oriFunctionCode; }
    void SetAnswerRequestSerialNumber(std::string& requestSerialNumber) { m_answer.m_requestSerialN = requestSerialNumber; }

	// 解析请求包
	static bool ParseRequestMessage(uint64_t lineNum, const std::string& requestMessage, tagMidRequst& midRequest);
	// 解析应答包
	static bool ParseAnswerMessage(uint64_t lineNum, const std::string& answerMessage, tagMidAnswer& midAnswer);
	// 解析mid日志
	static bool ParseMidMessage(uint64_t lineNum, const std::string& midMessage, tagMidRequst& midRequest, tagMidAnswer& midAnswer);

	// 获取请求包头
	tagMidRequst GetRequestHeader();
	// 获取请求包头的各个字段的值
    uint32_t GetRequestHeaderLength() { return m_request.m_headerLength; }
    uint32_t GetRequestDataLength() { return m_request.m_dataLength; }
	std::string GetRequestSerialNumber() { return m_request.m_requestSerialN; }
    std::string GetRequestCRCCheckCode() { return m_request.m_crcCheckCode; }
    std::string GetRequestVersionNumber() { return m_request.m_versionNumber; }
    std::string GetRequestUserCode() { return m_request.m_userCode; }
    std::string GetRequestOperationSite() { return m_request.m_operationSite; }
    std::string GetRequestAccountBranch() { return m_request.m_accountBranch; }
    std::string GetRequestOperationChannel() { return m_request.m_operationChannel; }
    std::string GetRequestSessionNumber() { return m_request.m_sessionNumber; }
    std::string GetRequestUserRole() { return m_request.m_userRole; }
    std::string GetRequestRouteFlag() { return m_request.m_routeFlag; }
    std::string GetRequestFunctionCode() { return m_request.m_reqFunctionCode; }
    std::string GetRequestData() { return m_request.requestData; }

	// 获取应答包头
	tagMidAnswer GetAnswerHeader();
	// 获取应答包头的各个字段的值
	uint32_t GetAnswerHeaderLength() { return m_answer.m_headerLength; }
	uint32_t GetAnswerDataLength() { return m_answer.m_dataLength; }
	std::string GetAnswerCRCCheckCode() { return m_answer.m_crcCheckCode; }
	std::string GetAnswerVersionNumber() { return m_answer.m_versionNumber; }
	std::string GetAnswerResponseCode() { return m_answer.m_responseCode; }
	std::string GetAnswerResponseMessage() { return m_answer.m_responseMessage; }
	std::string GetAnswerMorePacketsFlag() { return m_answer.m_morePacketsFlag; }
	uint32_t GetAnswerRespFieldCount() { return m_answer.m_respFieldCount; }
	uint32_t GetAnswerRespRecordCount() { return m_answer.m_respRecordCount; }
	std::string GetAnswerOriFunctionCode() { return m_answer.m_oriFunctionCode; }
	std::string GetAnswerRequestSerialNumber() { return m_answer.m_requestSerialN; }
	std::vector<std::pair<std::string, std::string>> GetAnswerData() { return m_answer.responseData;	}

private:
	std::string serialize(const tagMidRequst& request);
	std::string serialize(const tagMidAnswer& answer);

private:
	tagMidRequst m_request;
	tagMidAnswer m_answer;

	std::atomic_int64_t m_lineBegin = 0;
};

#endif