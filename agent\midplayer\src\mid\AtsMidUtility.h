﻿#ifndef __MID_UTILITY_H__
#define __MID_UTILITY_H__
#pragma once
#include <fstream>
#include "platform.h"

typedef std::chrono::microseconds microseconds;
typedef std::chrono::high_resolution_clock high_resolution_clock;
typedef std::chrono::time_point<std::chrono::high_resolution_clock> high_time_point;
typedef std::unordered_map<std::string, int> skipValMap;
typedef std::map<std::string, std::map<std::string, std::string>> IniValMap;
typedef std::unordered_map<std::string, std::queue<std::string>> orderMap;

typedef struct kdgwConfigTag
{
	std::string server;
	std::string port;
	std::string signstr;
}kdgwConfig;

typedef struct AplicationConfigTag
{
	bool readonly{ false };
	bool stgenable{ true };
	bool utilmatch{ true };
	uint32_t memchr{ 2 };
	uint32_t retrytime{ 8 };
	uint32_t userfiled{ 5 };
	uint32_t funcfiled{ 13 };
	uint32_t stkcfiled{ 19 };
	uint32_t typefiled{ 20 };
	uint32_t returnfiled{ 5 };
	uint32_t debuglevel{ 0 };
	uint32_t threadnum{ 1 };
	uint32_t batchnum{ 30000 };

	std::string startflag;
	std::string reqmflag;
	std::string ansmflag;
	std::string checkins;
	std::string filename;

	IniValMap configers;
	skipValMap funclbm;
	skipValMap markets;
	high_time_point clock{ std::chrono::high_resolution_clock::now() };
}AplicationConfig;

namespace ats
{
	void init_application(int argc, char* argv[]);

	// 日志系统初始化函数
	void init_logging_system(const std::string& config_file = "");

	std::string get_last_midfile();

	void reset_elapsed_time();

	void print_elapsed_time(const char* actionDescription = "");

	void* memchr_optimized(char* ptr, char* end, int value);

	char* strchr(char* src, int c, int num);
	char* GetValue(const char* src, char* dest, int num, char ch);
	char* GetValue(const char* src, char* dest, int num, char ch, int count);
	char* ltrim(char* str);
	char* rtrim(char* str);
	char* alltrim(char* str);
	char* padl(char* str, char ch, int num);
	char* padr(char* str, char ch, int num);
	char* padc(char* str, char ch, int num);
	long ahtol(char* str);
}

class AtsTimer
{
public:
	AtsTimer() { m_beginTime = std::chrono::high_resolution_clock::now(); }

	void begin() { m_beginTime = std::chrono::high_resolution_clock::now(); }
	void stop() { m_endTime = std::chrono::high_resolution_clock::now(); }
	int64_t elapsed() { return std::chrono::duration_cast<std::chrono::microseconds>(m_endTime - m_beginTime).count();}
	int64_t elapsed_second() { return std::chrono::duration_cast<std::chrono::seconds>(m_endTime - m_beginTime).count(); }

	auto getbegin() {	return m_beginTime;	}
	auto getstop() { return m_endTime; }

private:
	std::chrono::time_point<std::chrono::high_resolution_clock> m_beginTime;
	std::chrono::time_point<std::chrono::high_resolution_clock> m_endTime;
};

class AtsTimeStat
{
public:
	AtsTimeStat() { m_totalTime = 0; }
	~AtsTimeStat() { print(); }

	void begin() { m_axTimer.begin(); }

	void stop() { 
		m_axTimer.stop();
		m_totalTime += m_axTimer.elapsed();
	}

	int64_t current() {
		m_axTimer.stop();
		return m_axTimer.elapsed_second();
	}

	void reset() {
		m_totalTime = 0;
		m_printFlag = false;
		m_axTimer.begin();
	}

	auto get_start_time() { return m_axTimer.getbegin(); }

	int64_t total_time() { return m_totalTime; }

	void print();
	void print(const std::string& keyword);

private:
	AtsTimer m_axTimer;
	int64_t m_totalTime;
	bool m_printFlag = false;
};

extern AplicationConfig appConfig;

class ConsistentHash {
public:
	ConsistentHash(size_t virtualNodeCountPerNode) : virtualNodeCountPerNode(virtualNodeCountPerNode) {}

	void addNode(size_t nodeId);
	void removeNode(size_t nodeId);
	size_t getNodeFor(const std::string& key);

private:
	struct NodeHash {
		size_t nodeId;
		size_t hashValue;

		bool operator<(const NodeHash& other) const {
			return hashValue < other.hashValue;
		}
		// ������������
		bool operator==(const NodeHash& other) const {
			return nodeId == other.nodeId && hashValue == other.hashValue;
		}
	};

	size_t virtualNodeCountPerNode;
	std::vector<NodeHash> virtualNodes;
	std::map<size_t, std::vector<NodeHash*>> nodeToVirtualNodesMap;
	std::hash<std::string> hasher;

	// �������������ڸ���nodeToVirtualNodesMap
	void updateNodeToVirtualNodesMap() {
		nodeToVirtualNodesMap.clear();
		for (auto& vh : virtualNodes) {
			nodeToVirtualNodesMap[vh.nodeId].push_back(&vh);
		}
	}

	// ������removeNodeʱ�ҵ���ɾ����Ӧ��virtualNodes
	void removeVirtualNodesForNodeId(size_t nodeId) {
		//auto it = nodeToVirtualNodesMap.find(nodeId);
		//if (it != nodeToVirtualNodesMap.end()) {
		//	for (NodeHash* vhPtr : it->second) {
		//		auto vhIt = std::find(virtualNodes.begin(), virtualNodes.end(), *vhPtr);
		//		if (vhIt != virtualNodes.end()) {
		//			virtualNodes.erase(vhIt);
		//		}
		//	}
		//	nodeToVirtualNodesMap.erase(it);
		//}

		// �����Ѿ�ɾ���˽ڵ㣬����Ҫ��������virtualNodes�����nodeToVirtualNodesMap
		virtualNodes.erase(std::remove_if(virtualNodes.begin(), virtualNodes.end(),
			[nodeId](const NodeHash& nh) { return nh.nodeId == nodeId; }),
			virtualNodes.end());

		// ���nodeToVirtualNodesMap�ж�Ӧ����Ŀ
		nodeToVirtualNodesMap.erase(nodeId);
	}

	// ������������virtualNodes������nodeToVirtualNodesMap
	void rebuildVirtualNodes() {
		std::sort(virtualNodes.begin(), virtualNodes.end());
		updateNodeToVirtualNodesMap();
	}
};

template <typename T>
class ThreadSafeQueue {
public:
	void push(const T& value) {
		std::lock_guard<std::mutex> lock(mutex_);
		queue_.push(value);
		cond_var_.notify_one();
	}

	bool pop(T& value) {
		std::unique_lock<std::mutex> lock(mutex_);
		cond_var_.wait(lock, [this] { return !queue_.empty(); });
		value = queue_.front();
		queue_.pop();
		return true;
	}

private:
	std::queue<T> queue_;
	std::mutex mutex_;
	std::condition_variable cond_var_;
};

#endif