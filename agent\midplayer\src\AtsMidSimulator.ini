[main]
#只读模式，数据不会送往MID网关
read_only=false
#报盘开关，报盘和撮合的模拟成交
stg_enable=false
#等待成交，等待成交回报信息返回
util_match=false
#起始标志，原始MID日志起始字符
begin_mid=ipdst
#memchr算法，影响竖线分割性能
memchr_algo=find
#调试阶段，控制程序运行逻辑过程
debug_level=1999
#批次数目，批量处理MID请求数目
batch_num=51200
#线程数目，并发处理MID日志线程
thread_num=128
#重试次数，查询委托信息失败重试
retry_count=8
#客户字段，竖线分割第五个字段值
user_filed=5
#功能字段，竖线分割第十三字段值
func_filed=13
#股票字段，竖线分割第十九字段值
stkc_filed=19
#买卖行为，竖线分割第二十字段值
type_filed=20
#回溯结果，应答分割第五个字段值
code_filed=5

[mid_cfg]
#请求报文，切割请求内容的字符串
req_flag=REQ:
#应答报文，切割应答内容的字符串
ans_flag=RESP:
#跳过LBM，不需要回溯请求功能号
skip_func=90,100,301,368,1760
#跳过市场，不需要回溯的买卖市场
skip_market=20,30

[kdg_std]
#网关地址，这里是标准的MID网关************ ***********
server=************
#网关端口，标准的MID网关IP地址
port=9100
#超时时间，标准的MID网关端口号
timeout=45
#签入配置，连接MID网关签入配置
checkin=0000|0000|CRCCRCCR|KDGATEWAY1.2|9988|127.0.0.1|999|4|SS|2|
#协议版本号
version=KDGATEWAY1.2
#CRC校验码
crc_code=12345678
#工作密钥
work_key=12345678

[kdg_tdx]
#网关地址，这里是通达信MID网关
server=***********
#网关端口，通达信MID网关IP地址
port=9200
#超时时间，通达信MID网关端口号
timeout=45
#签入配置，连接MID网关签入配置
checkin=0000|0000|CRCCRCCR|KDGATEWAY1.2|9988|127.0.0.1|999|4|SS|2|
#协议版本号
version=KDGATEWAY1.2
#CRC校验码
crc_code=12345678
#工作密钥
work_key=12345678

[logging]
# 日志配置 - spdlog设置
log_level=INFO           # 日志级别：TRACE, DEBUG, INFO, WARN, ERROR, CRITICAL
log_file=logs/midplayer.log  # 日志文件路径
rotation_type=daily      # 轮转类型：daily（按天）, size（按大小）
rotation_hour=0          # 按天轮转的小时（0-23，默认0点）
rotation_minute=0        # 按天轮转的分钟（0-59，默认0分）
max_file_size=10485760   # 按大小轮转时的最大文件大小（字节，10MB）
max_files=30             # 最大保留文件数
console_output=true      # 是否输出到控制台
file_output=true         # 是否输出到文件
single_thread=true       # 使用单线程日志

# 日志格式配置
console_pattern=%Y-%m-%d %H:%M:%S.%e [%t] [%^%l%$] %n - %v
file_pattern=%Y-%m-%d %H:%M:%S.%e [%t] [%l] %n - %v

# 按天轮转说明：
# - 文件名格式：midplayer_YYYY-MM-DD.log
# - 每天凌晨0点0分自动创建新文件
# - 旧文件自动保留，超过max_files数量时自动删除最旧的文件

[performance]
# 性能配置
# 内存映射文件缓冲区大小（字节）
mmap_buffer_size=67108864    # 64MB

# 网络发送缓冲区大小（字节）
send_buffer_size=65536       # 64KB

# 网络接收缓冲区大小（字节）
recv_buffer_size=65536       # 64KB

# 消息处理批次大小
message_batch_size=1000

# 统计信息更新间隔（毫秒）
stats_update_interval=1000

# 进度显示更新间隔（毫秒）
progress_update_interval=500

[network]
# 网络连接配置
# 连接超时时间（秒）
connect_timeout=30

# 发送超时时间（秒）
send_timeout=10

# 接收超时时间（秒）
recv_timeout=10

# 心跳间隔（秒）
heartbeat_interval=30

# 重连间隔（秒）
reconnect_interval=5

# 最大重连次数
max_reconnect_attempts=3
