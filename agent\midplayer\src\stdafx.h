﻿#ifndef _STD_ATSMAFX_H__
#define _STD_ATSMAFX_H__
#pragma once
#define WIN32_LEAN_AND_MEAN
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <cstdlib>
#include <queue>
#include <mutex>
#include <filesystem>
#include <condition_variable>
#include "AxCommon.h"
#include "AtsMidUtility.h"
#include "AtsResource.h"

void* fast_memchr(void* haystack, int n, size_t len);
void* sse2_memchr(void* haystack, int n, size_t len);


#endif
