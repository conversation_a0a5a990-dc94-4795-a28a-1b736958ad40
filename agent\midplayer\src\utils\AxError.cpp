﻿#include "stdafx.h"
#include "AxError.h"
#include <sstream>

MidPlayerErrorManager& MidPlayerErrorManager::getInstance() {
	static MidPlayerErrorManager instance;
	return instance;
}

MidPlayerErrorManager::MidPlayerErrorManager() {
	initializeErrorMessages();
}

void MidPlayerErrorManager::initializeErrorMessages() {
	// 成功状态
	errorMessages_[MidPlayerError::SUCCESS] = "操作成功";

	// 文件操作错误 (1000-1999)
	errorMessages_[MidPlayerError::FILE_NOT_FOUND] = "文件未找到";
	errorMessages_[MidPlayerError::FILE_OPEN_FAILED] = "文件打开失败";
	errorMessages_[MidPlayerError::FILE_READ_FAILED] = "文件读取失败";
	errorMessages_[MidPlayerError::FILE_WRITE_FAILED] = "文件写入失败";
	errorMessages_[MidPlayerError::FILE_MAPPING_FAILED] = "文件内存映射失败";
	errorMessages_[MidPlayerError::FILE_SIZE_ERROR] = "文件大小错误";

	// 网络通信错误 (2000-2999)
	errorMessages_[MidPlayerError::NETWORK_CONNECTION_FAILED] = "网络连接失败";
	errorMessages_[MidPlayerError::NETWORK_SEND_FAILED] = "网络发送失败";
	errorMessages_[MidPlayerError::NETWORK_RECEIVE_FAILED] = "网络接收失败";
	errorMessages_[MidPlayerError::NETWORK_TIMEOUT] = "网络超时";
	errorMessages_[MidPlayerError::NETWORK_SOCKET_ERROR] = "Socket错误";
	errorMessages_[MidPlayerError::NETWORK_DISCONNECT] = "网络连接断开";

	// 协议解析错误 (3000-3999)
	errorMessages_[MidPlayerError::PROTOCOL_INVALID_MESSAGE] = "无效的协议消息";
	errorMessages_[MidPlayerError::PROTOCOL_CRC_ERROR] = "CRC校验失败";
	errorMessages_[MidPlayerError::PROTOCOL_VERSION_MISMATCH] = "协议版本不匹配";
	errorMessages_[MidPlayerError::PROTOCOL_FIELD_EXTRACT_FAILED] = "协议字段提取失败";
	errorMessages_[MidPlayerError::PROTOCOL_MESSAGE_TOO_SHORT] = "协议消息长度不足";
	errorMessages_[MidPlayerError::PROTOCOL_MESSAGE_INCOMPLETE] = "协议消息不完整";

	// 配置错误 (4000-4999)
	errorMessages_[MidPlayerError::CONFIG_FILE_NOT_FOUND] = "配置文件未找到";
	errorMessages_[MidPlayerError::CONFIG_PARSE_ERROR] = "配置文件解析错误";
	errorMessages_[MidPlayerError::CONFIG_INVALID_VALUE] = "配置值无效";
	errorMessages_[MidPlayerError::CONFIG_MISSING_SECTION] = "配置段缺失";
	errorMessages_[MidPlayerError::CONFIG_MISSING_KEY] = "配置键缺失";

	// 系统资源错误 (5000-5999)
	errorMessages_[MidPlayerError::MEMORY_ALLOCATION_FAILED] = "内存分配失败";
	errorMessages_[MidPlayerError::THREAD_CREATE_FAILED] = "线程创建失败";
	errorMessages_[MidPlayerError::MUTEX_LOCK_FAILED] = "互斥锁获取失败";
	errorMessages_[MidPlayerError::SYSTEM_RESOURCE_EXHAUSTED] = "系统资源耗尽";

	// 业务逻辑错误 (6000-6999)
	errorMessages_[MidPlayerError::INVALID_PARAMETER] = "无效参数";
	errorMessages_[MidPlayerError::OPERATION_NOT_SUPPORTED] = "操作不支持";
	errorMessages_[MidPlayerError::STATE_ERROR] = "状态错误";
	errorMessages_[MidPlayerError::AUTHENTICATION_FAILED] = "认证失败";
	errorMessages_[MidPlayerError::AUTHORIZATION_FAILED] = "授权失败";

	// 未知错误
	errorMessages_[MidPlayerError::UNKNOWN_ERROR] = "未知错误";
}

std::string MidPlayerErrorManager::getErrorMessage(MidPlayerError errorCode) const {
	auto it = errorMessages_.find(errorCode);
	if (it != errorMessages_.end()) {
		return it->second;
	}
	return "未定义的错误码: " + std::to_string(static_cast<int>(errorCode));
}

void MidPlayerErrorManager::setCustomErrorMessage(MidPlayerError errorCode, const std::string& message) {
	errorMessages_[errorCode] = message;
}

std::string MidPlayerErrorManager::formatError(MidPlayerError errorCode, const std::string& context) const {
	std::ostringstream oss;
	oss << "[错误码:" << static_cast<int>(errorCode) << "] " << getErrorMessage(errorCode);
	if (!context.empty()) {
		oss << " - 上下文: " << context;
	}
	return oss.str();
}
