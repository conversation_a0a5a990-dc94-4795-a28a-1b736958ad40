﻿#ifndef _ATS_DISPATCHER_H__
#define _ATS_DISPATCHER_H__
#pragma once
#include <queue>
#include <future>

// 线程池中的任务类型
using Task = std::function<void()>;

namespace progschj {

	class ThreadPool {
	public:
		explicit ThreadPool(std::size_t threads
			= std::max<std::size_t>(2u, std::thread::hardware_concurrency()));

		template<class F, class... Args>
		auto enqueue(F&& f, Args&&... args)
			-> std::future<
#if defined(__cpp_lib_is_invocable) && __cpp_lib_is_invocable >= 201703
			typename std::invoke_result<F&&, Args&&...>::type
#else
			typename std::result_of<F && (Args&&...)>::type
#endif
			>;

		template<class F, class... Args>
		auto submit(const std::string& userCode, F&& f, Args&&... args)
			->std::future<
#if defined(__cpp_lib_is_invocable) && __cpp_lib_is_invocable >= 201703
			typename std::invoke_result<F&&, Args&&...>::type
#else
			typename std::result_of<F && (Args&&...)>::type
#endif
			>;

		std::future<int> emplace(const std::string& user_code, const std::string& row, uint64_t line);
	
		std::thread::id get_least_busy_thread();

		void wait_until_empty();
		void wait_until_nothing_in_flight();
		void set_queue_size_limit(std::size_t limit);
		void set_pool_size(std::size_t limit);
		void stop_pool();
		~ThreadPool();

	private:
		void start_worker(std::size_t worker_number, std::unique_lock<std::mutex> const& lock);

		// 线程池线程
		std::vector< std::thread > workers;
		// 线程池大小
		std::size_t pool_size;
		// 线程池队列
		std::queue< std::function<void()> > global_tasks;
		// 客户任务池
		std::unordered_map<std::string, std::thread::id> user_thread;
		std::unordered_map<std::thread::id, std::queue<Task>> user_tasks;
		// 队列最大值
		std::size_t max_queue_size = 100000;
		// 停止标志位
		bool stop = false;

		// synchronization
		std::mutex queue_mutex;
		std::condition_variable condition_producers;
		std::condition_variable condition_consumers;

		std::mutex in_flight_mutex;
		std::condition_variable in_flight_condition;
		std::atomic<std::size_t> in_flight;

		struct handle_in_flight_decrement
		{
			ThreadPool& tp;

			handle_in_flight_decrement(ThreadPool& tp_)
				: tp(tp_)
			{ }

			~handle_in_flight_decrement()
			{
				std::size_t prev
					= std::atomic_fetch_sub_explicit(&tp.in_flight,
						std::size_t(1),
						std::memory_order_acq_rel);
				if (prev == 1)
				{
					std::unique_lock<std::mutex> guard(tp.in_flight_mutex);
					tp.in_flight_condition.notify_all();
				}
			}
		};
	};

	// the constructor just launches some amount of workers
	inline ThreadPool::ThreadPool(std::size_t threads)
		: pool_size(threads)
		, in_flight(0)
	{
		std::unique_lock<std::mutex> lock(this->queue_mutex);
		for (std::size_t i = 0; i != threads; ++i)
			start_worker(i, lock);
	}

	// add new work item to the pool
	template<class F, class... Args>
	auto ThreadPool::enqueue(F&& f, Args&&... args)
		->std::future<
#if defined(__cpp_lib_is_invocable) && __cpp_lib_is_invocable >= 201703
		typename std::invoke_result<F&&, Args&&...>::type
#else
		typename std::result_of<F && (Args&&...)>::type
#endif
		>
	{
#if defined(__cpp_lib_is_invocable) && __cpp_lib_is_invocable >= 201703
		using return_type = typename std::invoke_result<F&&, Args&&...>::type;
#else
		using return_type = typename std::result_of<F && (Args&&...)>::type;
#endif

		auto task = std::make_shared< std::packaged_task<return_type()> >(
			std::bind(std::forward<F>(f), std::forward<Args>(args)...)
		);

		std::future<return_type> res = task->get_future();

		std::unique_lock<std::mutex> lock(queue_mutex);
		if (global_tasks.size() >= max_queue_size)
			// wait for the queue to empty or be stopped
			condition_producers.wait(lock,
				[this]
				{
					return global_tasks.size() < max_queue_size
						|| stop;
				});

		// don't allow enqueueing after stopping the pool
		if (stop)
			throw std::runtime_error("enqueue on stopped ThreadPool");

		global_tasks.emplace([task]() { (*task)(); });
		std::atomic_fetch_add_explicit(&in_flight,
			std::size_t(1),
			std::memory_order_relaxed);
		condition_consumers.notify_one();

		return res;
	}

	static int run_count = 0, run_tasks = 0;

	template<class F, class... Args>
	auto ThreadPool::submit(const std::string& user_code, F&& f, Args&&... args)
		->std::future<
#if defined(__cpp_lib_is_invocable) && __cpp_lib_is_invocable >= 201703
		typename std::invoke_result<F&&, Args&&...>::type
#else
		typename std::result_of<F && (Args&&...)>::type
#endif
		>
	{
#if defined(__cpp_lib_is_invocable) && __cpp_lib_is_invocable >= 201703
		using return_type = typename std::invoke_result<F&&, Args&&...>::type;
#else
		using return_type = typename std::result_of<F && (Args&&...)>::type;
#endif
		std::future<return_type> res;
		if (1 == appConfig.debugnum)
			return res;

		auto task = std::make_shared< std::packaged_task<return_type()> >(
			std::bind(std::forward<F>(f), std::forward<Args>(args)...)
		);

		if (2 == appConfig.debugnum)
			return res;

		res = task->get_future();
		if (3 == appConfig.debugnum)
			return res;

		std::unique_lock<std::mutex> lock(queue_mutex);
		// 检查是否已经有线程处理这个用户号
		auto it = user_thread.find(user_code);
		if (it == user_thread.end()) {
			// 寻找空闲线程号
			std::thread::id special_thread = get_least_busy_thread();
			// 给用户分配线程
			it = user_thread.emplace(user_code, special_thread).first;
		}

		// 将新任务添加到队列中
		user_tasks[it->second].emplace([task]() { (*task)(); });

		std::atomic_fetch_add_explicit(&in_flight,
			std::size_t(1),
			std::memory_order_relaxed);


		run_tasks++;
		//condition_consumers.notify_one();
		condition_consumers.notify_all();

		return res;
	}

	inline std::future<int> ThreadPool::emplace(const std::string& user_code, const std::string& row, uint64_t line)
	{
		std::future<int> res;
		if (1 == appConfig.debugnum)
			return res;

		auto task = std::make_shared< std::packaged_task<int()> >(
			[user_code, row, line]() {
				return 0;
			}
		);

		if (2 == appConfig.debugnum)
			return res;

		res = task->get_future();
		if (3 == appConfig.debugnum)
			return res;

		std::unique_lock<std::mutex> lock(queue_mutex);
		if (4 == appConfig.debugnum)
			return res;

		// 检查是否已经有线程处理这个用户号
		auto it = user_thread.find(user_code);
		if (it == user_thread.end()) {
			// 寻找空闲线程号
			std::thread::id special_thread = get_least_busy_thread();
			// 给用户分配线程
			it = user_thread.emplace(user_code, special_thread).first;
		}
		if (5 == appConfig.debugnum)
			return res;

		// 将新任务添加到队列中
		user_tasks[it->second].emplace([task]() { (*task)(); });
		if (6 == appConfig.debugnum)
			return res;

		std::atomic_fetch_add_explicit(&in_flight,
			std::size_t(1),
			std::memory_order_relaxed);
		if (7 == appConfig.debugnum)
			return res;

		run_tasks++;
		condition_consumers.notify_all();

		return res;
	}

	inline std::thread::id ThreadPool::get_least_busy_thread()
	{
		// 初始化最小任务数量为最大值
		int min_task_count = (std::numeric_limits<int>::max)();
		std::thread::id least_busy_thread_id = workers[0].get_id();

		// 遍历所有线程
		for (const auto& pair : user_tasks) {
			const std::thread::id& thread_id = pair.first;
			int task_count = pair.second.size();

			// 如果该线程的任务数量小于当前最小任务数量，则更新最小任务数量和最不忙的线程
			if (task_count < min_task_count) {
				min_task_count = task_count;
				least_busy_thread_id = thread_id;
			}
		}

		return least_busy_thread_id;
	}

	// the destructor joins all threads
	inline ThreadPool::~ThreadPool()
	{
		std::unique_lock<std::mutex> lock(queue_mutex);
		stop = true;
		pool_size = 0;
		condition_consumers.notify_all();
		condition_producers.notify_all();
		condition_consumers.wait(lock, [this] { return this->workers.empty(); });
		assert(in_flight == 0);
	}

	inline void ThreadPool::wait_until_empty()
	{
		{
			std::unique_lock<std::mutex> lock(this->queue_mutex);
			this->condition_producers.wait(lock,
				[this] { return this->global_tasks.empty(); });
		}
	}

	inline void ThreadPool::wait_until_nothing_in_flight()
	{
		std::unique_lock<std::mutex> lock(this->in_flight_mutex);
		this->in_flight_condition.wait(lock,
			[this] { return this->in_flight == 0; });
	}

	inline void ThreadPool::set_queue_size_limit(std::size_t limit)
	{
		std::unique_lock<std::mutex> lock(this->queue_mutex);

		if (stop)
			return;

		std::size_t const old_limit = max_queue_size;
		max_queue_size = (std::max)(limit, std::size_t(1));
		if (old_limit < max_queue_size)
			condition_producers.notify_all();
	}

	inline void ThreadPool::set_pool_size(std::size_t limit)
	{
		if (limit < 1)
			limit = 1;

		std::unique_lock<std::mutex> lock(this->queue_mutex);

		if (stop)
			return;

		std::size_t const old_size = pool_size;
		assert(this->workers.size() >= old_size);

		pool_size = limit;
		if (pool_size > old_size)
		{
			// create new worker threads
			// it is possible that some of these are still running because
			// they have not stopped yet after a pool size reduction, such
			// workers will just keep running
			for (std::size_t i = old_size; i != pool_size; ++i)
				start_worker(i, lock);
		}
		else if (pool_size < old_size)
			// notify all worker threads to start downsizing
			this->condition_consumers.notify_all();
	}

	inline void ThreadPool::start_worker(
		std::size_t worker_number, std::unique_lock<std::mutex> const& lock)
	{
		assert(lock.owns_lock() && lock.mutex() == &this->queue_mutex);
		assert(worker_number <= this->workers.size());

		auto worker_func =
			[this, worker_number]
			{
				std::thread::id thread_id = std::this_thread::get_id();

				for (;;)
				{
					bool notify;
					std::function<void()> task;

					{
						// 等待直到有任务或者线程池停止
						std::unique_lock<std::mutex> lock(this->queue_mutex);
						this->condition_consumers.wait(lock,
							[this, worker_number, thread_id] {
								return this->stop || !this->global_tasks.empty() || !this->user_tasks[thread_id].empty()
									|| pool_size < worker_number + 1; });

						run_count += 1;

						// deal with downsizing of thread pool or shutdown
						if ((this->stop && this->global_tasks.empty() && this->user_tasks[thread_id].empty())
							|| (!this->stop && pool_size < worker_number + 1))
						{
							// detach this worker, effectively marking it stopped
							this->workers[worker_number].detach();
							// downsize the workers vector as much as possible
							while (this->workers.size() > pool_size
								&& !this->workers.back().joinable())
								this->workers.pop_back();
							// if this is was last worker, notify the destructor
							if (this->workers.empty())
								this->condition_consumers.notify_all();
							return;
						}
						else if (!this->global_tasks.empty())
						{
							task = std::move(this->global_tasks.front());
							this->global_tasks.pop();
							notify = this->global_tasks.size() + 1 == max_queue_size
								|| this->global_tasks.empty();
						}
						else if (!this->user_tasks[thread_id].empty())
						{
							task = std::move(this->user_tasks[thread_id].front());
							this->user_tasks[thread_id].pop();
							notify = this->global_tasks.size() + 1 == max_queue_size
								|| this->global_tasks.empty();
						}
						else
							continue;
					}

					handle_in_flight_decrement guard(*this);

					if (notify)
					{
						std::unique_lock<std::mutex> lock(this->queue_mutex);
						condition_producers.notify_all();
					}

					task();
				}
			};

		if (worker_number < this->workers.size()) {
			std::thread& worker = this->workers[worker_number];
			// start only if not already running
			if (!worker.joinable()) {
				worker = std::thread(worker_func);
			}
		}
		else {
			this->workers.push_back(std::thread(worker_func));
			this->user_tasks.emplace(workers[worker_number].get_id(), std::queue<Task>());
		}
	}

	// 停止线程池，等待所有任务完成
	inline void ThreadPool::stop_pool() {
		std::unique_lock<std::mutex> lock(this->queue_mutex);
		this->stop = true;
		lock.unlock();

		condition_consumers.notify_all();
		condition_producers.notify_all();
		condition_consumers.wait(lock, [this] { return this->workers.empty(); });
	}
}

#endif