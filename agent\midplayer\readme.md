# MidPlayer - MID协议流量回放执行引擎

## 项目概述

MidPlayer是ArkReplay-Agent系统中专门用于MID协议流量回放的高性能执行引擎，采用C++17实现。作为ArkReplay系统的核心组件之一，MidPlayer专注于金融交易系统中MID（Market Information Distribution）协议的数据解析、回放和网络通信功能。

### 在ArkReplay-Agent中的定位

MidPlayer是ArkReplay-Agent四层架构中**流量回放执行引擎层**的重要组成部分：

- **ArkReplay-Web (Vue.js/TypeScript)**：用户交互界面
- **ArkReplay-Server (Python/Django)**：服务管理与配置中心  
- **ArkReplay-Agent (C++)**：高性能流量回放执行引擎
  - **tcpplayer**：TCP协议回放引擎
  - **udpplayer**：UDP协议回放引擎
  - **midplayer**：MID协议专用回放引擎 ⭐
- **ArkReplay DB-Agent (Python/FastAPI)**：数据库操作服务

## 核心特性

### 🚀 高性能设计
- **内存映射文件读取**：支持大文件高效处理，减少I/O开销
- **多种内存优化算法**：SSE2优化、快速查找、标准库算法可选
- **零拷贝架构**：移动语义和智能指针，避免不必要的数据拷贝
- **多线程支持**：可配置线程数量，充分利用多核CPU性能

### 🔧 MID协议专业支持
- **完整的MID协议栈**：支持KDGATEWAY1.2版本协议
- **消息格式解析**：支持请求/响应消息的完整解析
- **CRC校验**：内置CRC校验机制确保数据完整性
- **加密支持**：集成Blowfish加密算法
- **网络通信**：TCP Socket通信，支持异步I/O

### 📊 实时监控与统计
- **进度跟踪**：实时显示文件处理进度和速度
- **性能统计**：精确的时间测量和性能分析
- **错误处理**：完善的错误检测和异常处理机制
- **调试支持**：多级别调试输出，便于问题定位

## 技术架构

### 核心组件架构

```
midplayer/
├── src/
│   ├── AtsMidSimulator.cpp      # 主程序入口
│   ├── AtsMidParser.h/.cpp      # MID协议解析器
│   ├── mid/
│   │   ├── MidGateway.h/.cpp    # MID协议网关
│   │   ├── AtsMidUtility.h/.cpp # 工具函数库
│   │   ├── AtsResource.h        # 资源管理
│   │   ├── crc.h/.cpp          # CRC校验算法
│   │   ├── Blowfish.h/.cpp     # 加密算法
│   │   └── MiniSocket.h/.cpp   # 网络通信
│   └── utils/
│       ├── AxLogger.h/.cpp     # 统一日志系统 ⭐
│       ├── AxConfig.h/.cpp     # 配置管理系统
│       └── AXCommon.h/.cpp     # 通用工具函数
├── config/
│   ├── AtsMidSimulator.ini     # 主配置文件
│   └── AtsMidSimulator.cfg     # 日志配置文件
└── midplayer.vcxproj          # Visual Studio项目文件
```

### 数据流处理架构

```
文件输入 → 内存映射 → 协议解析 → 消息处理 → 网络发送 → 响应收集
    ↓         ↓         ↓         ↓         ↓         ↓
  大文件    零拷贝    字段提取   格式转换   TCP通信   结果统计
```

## 核心类和方法说明

### AtsMidParser 类
MID协议解析器的核心实现，负责文件读取和数据解析。

#### 主要方法：
- `bool win_readNetis_Bymmap(const std::string& filename)`
  - Windows平台内存映射文件读取
  - 支持大文件高效处理
  - 实时进度显示和性能统计

- `bool linux_readNetis_Bymmap(const std::string& filename)`  
  - Linux平台内存映射文件读取
  - 跨平台兼容性支持

- `static bool isValidMidRequest(uint64_t line, std::string& row)`
  - 验证MID请求消息的有效性
  - 支持消息格式检查

- `static bool extractReqRes(uint64_t line, const std::string& row, std::string& req, std::string& res)`
  - 从消息行中提取请求和响应数据
  - 支持字段分离和数据提取

- `static std::string extractFields(uint64_t line, const std::string& row, int field)`
  - 提取指定字段的数据
  - 支持多种字段类型

### MidGateway 类
MID协议网关实现，负责网络通信和协议处理。

#### 核心功能：
- **协议支持**：完整的KDGATEWAY1.2协议实现
- **消息结构**：
  - 请求消息：头部(4字节) + 数据长度(4字节) + CRC校验(8字节) + 版本信息 + 用户信息 + 数据内容
  - 响应消息：头部(4字节) + 数据长度 + CRC校验(8字节) + 版本信息 + 错误码 + 响应数据
- **网络通信**：TCP Socket连接，支持异步I/O
- **数据加密**：Blowfish算法加密支持
- **会话管理**：支持多会话并发处理

### ats命名空间工具函数

#### 应用程序管理：
- `void init_application(int argc, char* argv[])`
  - 初始化应用程序环境
  - 加载配置文件和资源
  - 设置工作目录和控制台属性

- `std::string get_last_midfile()`
  - 获取上次处理的MID文件路径
  - 支持用户交互选择文件
  - 自动保存文件处理历史

#### 性能统计：
- `void reset_elapsed_time()`
  - 重置计时器，开始性能测量

- `void print_elapsed_time(const char* actionDescription = nullptr)`
  - 打印执行时间统计
  - 支持高精度时间测量（小时:分钟:秒.毫秒.微秒）

#### 内存优化：
- `void* memchr_optimized(char* ptr, char* end, int value)`
  - 优化的内存查找函数
  - 支持多种算法：fast_memchr、sse2_memchr、std::find、std::memchr
  - 可通过配置选择最适合的算法

## 使用方式

### 1. 编译构建

**环境要求：**
- Visual Studio 2022或更高版本
- Windows 10/11 x64平台
- C++17标准支持

**构建步骤：**
```bash
# 使用Visual Studio
1. 打开 midplayer.vcxproj 项目文件
2. 选择 Release|x64 配置
3. 生成解决方案

# 或使用MSBuild命令行
msbuild midplayer.vcxproj /p:Configuration=Release /p:Platform=x64
```

### 2. 配置文件

**AtsMidSimulator.ini 主要配置项：**

```ini
[main]
read_only=false          # 只读模式
stg_enable=true          # 存储启用
util_match=true          # 工具匹配
begin_mid=0              # 开始MID标识
retry_count=3            # 重试次数
memchr_algo=fast         # 内存算法：fast/sse2/find/std
debug_level=1            # 调试级别：0-3
batch_num=1000           # 批处理数量
thread_num=0             # 线程数量（0=自动检测）

[kdg_std]
checkin=default          # 签入配置

[mid_cfg]
req_flag=REQ             # 请求标识
ans_flag=ANS             # 响应标识
skip_func=               # 跳过的功能（逗号分隔）
skip_market=             # 跳过的市场（逗号分隔）
```

### 3. 运行程序

```bash
# 直接运行
./midplayer.exe

# 程序会提示：
# 1. 选择是否使用上次处理的文件
# 2. 或输入新的MID文件路径
# 3. 显示处理进度和性能统计
# 4. 完成后等待用户按键退出
```

## 配置说明

### 内存优化算法选择

| 算法类型 | 配置值 | 适用场景 | 性能特点 |
|---------|--------|----------|----------|
| 快速算法 | fast | 通用场景 | 平衡性能和兼容性 |
| SSE2优化 | sse2 | 现代CPU | 利用SIMD指令加速 |
| STL查找 | find | 标准实现 | 跨平台兼容性好 |
| 标准库 | std | 保守选择 | 最高兼容性 |

### 调试级别设置

| 级别 | 说明 | 输出内容 |
|------|------|----------|
| 0 | 静默模式 | 仅错误信息 |
| 1 | 基本信息 | 进度和统计 |
| 2 | 详细信息 | 消息解析详情 |
| 3 | 调试模式 | 完整调试输出 |

### 线程配置优化

- **thread_num=0**：自动检测CPU核心数
- **thread_num=N**：手动指定线程数量
- **建议值**：CPU核心数的1-2倍

## 与其他组件的交互关系

### 1. 与ArkReplay-Server的交互
- **任务接收**：接收Server下发的回放任务
- **状态报告**：实时上报回放进度和状态
- **结果上传**：将回放结果发送给Server

### 2. 与ArkReplay DB-Agent的协作
- **数据准备**：为DB-Agent提供回放前的数据状态
- **结果比对**：配合DB-Agent进行回放前后数据比对
- **一致性验证**：确保回放数据的完整性

### 3. 与目标交易系统的通信
- **协议适配**：将MID协议消息转换为目标系统格式
- **网络连接**：建立与交易系统的TCP连接
- **消息发送**：按时序发送交易请求
- **响应收集**：收集并记录系统响应

## 性能特性

### 内存使用优化
- **内存映射**：大文件零拷贝读取
- **对象池**：减少动态内存分配
- **缓存友好**：数据结构优化CPU缓存使用

### 处理性能指标
- **文件读取速度**：>500MB/s（SSD存储）
- **消息解析速度**：>100,000条/秒
- **内存使用**：<100MB（处理GB级文件）
- **CPU使用率**：可配置多线程充分利用多核

### 网络通信性能
- **连接建立**：<100ms
- **消息发送延迟**：<1ms
- **并发连接数**：>1000
- **吞吐量**：>10,000 TPS

## 日志系统详解 ⭐

### 日志接口使用指南

#### 1. 格式化日志（推荐）
```cpp
// 现代C++格式化语法，高性能
MIDLOG_INFO("用户{}登录，IP: {}", username, client_ip);
MIDLOG_ERROR("连接失败，错误码: {}, 重试次数: {}", error_code, retry_count);

// LOG4CXX格式化版本
LOG4CXX_INFO_FMT(nullptr, "处理消息: {} 条，耗时: {} ms", count, elapsed);
MFLOG_DEBUG("调试信息: 线程ID={}, 内存使用={}MB", thread_id, memory_mb);
```

#### 2. 流式操作符日志（兼容性）
```cpp
// 支持传统的流式操作符，完全兼容现有代码
LOG4CXX_INFO(nullptr, "用户[" << username << "]从线程[" << thread_id << "]登录");
LOG4CXX_ERROR(nullptr, "错误: " << error_msg << ", 时间: " << timestamp);
```

#### 3. 条件日志和性能日志
```cpp
// 条件日志
MIDLOG_IF(debug_enabled, DEBUG, "调试信息: {}", debug_data);

// 性能监控
MIDLOG_PERF_START(database_query);
// ... 执行数据库查询
MIDLOG_PERF_END(database_query, "查询用户数据: {}", user_id);
```

### 日志配置

#### 配置文件示例
```cpp
// 通过MidPlayerConfig配置日志系统
MidPlayerConfig::LoggingConfig config;
config.logLevel = "info";                    // 日志级别
config.logFile = "logs/midplayer.log";       // 日志文件
config.consoleOutput = true;                 // 控制台输出
config.fileOutput = true;                    // 文件输出
config.rotationType = "daily";               // 轮转类型：daily/size
config.maxFileSize = 100 * 1024 * 1024;      // 最大文件大小
config.maxFiles = 30;                        // 最大文件数量
config.singleThread = false;                 // 多线程模式
```

#### 运行时配置
```cpp
// 获取日志管理器并配置
auto& logger_mgr = MidPlayerLogger::getInstance();
logger_mgr.initialize(config);
logger_mgr.setLogLevel("debug");             // 动态调整日志级别
```

### 错误处理机制
- **文件错误**：文件不存在、权限不足、格式错误
- **网络错误**：连接失败、超时、断线重连
- **协议错误**：消息格式错误、校验失败
- **系统错误**：内存不足、线程创建失败
- **日志错误**：自动降级处理，确保程序稳定运行

## 扩展和定制

### 协议扩展
- **新协议支持**：通过继承ProtocolParser基类
- **消息格式**：自定义消息解析和序列化
- **字段映射**：灵活的字段提取和转换

### 功能扩展
- **插件机制**：支持动态加载功能模块
- **配置驱动**：通过配置文件控制功能开关
- **回调接口**：提供消息处理回调机制

## 故障排除

### 常见问题

1. **文件读取失败**
   - 检查文件路径和权限
   - 确认文件格式正确
   - 查看磁盘空间是否充足

2. **网络连接问题**
   - 检查目标系统是否可达
   - 确认端口是否开放
   - 查看防火墙设置

3. **性能问题**
   - 调整线程数量配置
   - 选择合适的内存算法
   - 检查系统资源使用情况

4. **协议解析错误**
   - 检查消息格式是否符合MID协议规范
   - 确认CRC校验设置
   - 查看调试日志详细信息

### 调试技巧
- 使用debug_level=3获取详细日志
- 通过性能统计分析瓶颈
- 使用内存和CPU监控工具
- 分段测试定位问题范围

## MID协议技术细节

### 协议规范
MidPlayer实现的MID协议基于KDGATEWAY1.2版本，具有以下特点：

#### 消息结构
**请求消息格式：**
```
头部长度(4字节) + 数据总长度(4字节) + CRC校验(8字节) + 版本信息 +
用户信息(10字节) + 操作站点(64字节) + 营业部支(6字节) +
操作类型(1字节) + 会话标识(16字节) + 扩展字段1(20字节) +
扩展字段2(20字节) + 扩展字段3(20字节) + 数据内容
```

**响应消息格式：**
```
头部长度(4字节) + 数据长度 + CRC校验(8字节) + 版本信息 +
错误码(10字节) + 错误信息(200字节) + 后续标识(1字节) +
应用字段码(10字节) + 应用事件(10字节) + 原请求序号(20字节) +
操作流水号(20字节) + 扩展字段2(20字节) + 响应数据
```

#### 数据分隔符
- 字段分隔符：`|` (0x7C)
- 前台转义：`&#124;` → `|`
- 后台转义：`|` → `&#124;`

#### CRC校验算法
```cpp
// CRC-32校验实现
uint32_t calculate_crc32(const uint8_t* data, size_t length) {
    uint32_t crc = 0xFFFFFFFF;
    for (size_t i = 0; i < length; i++) {
        crc = crc_table[(crc ^ data[i]) & 0xFF] ^ (crc >> 8);
    }
    return crc ^ 0xFFFFFFFF;
}
```

### 加密机制
使用Blowfish算法进行数据加密：

```cpp
// 加密示例
CBlowFish blowfish((unsigned char*)"SZKINGDOM", 9);
blowfish.Encrypt(plaintext, ciphertext, length);
blowfish.Decrypt(ciphertext, plaintext, length);
```

## 高级配置

### 性能调优参数

```ini
[performance]
# 内存映射文件缓冲区大小（字节）
mmap_buffer_size=67108864    # 64MB

# 网络发送缓冲区大小（字节）
send_buffer_size=65536       # 64KB

# 网络接收缓冲区大小（字节）
recv_buffer_size=65536       # 64KB

# 消息处理批次大小
message_batch_size=1000

# 统计信息更新间隔（毫秒）
stats_update_interval=1000

# 进度显示更新间隔（毫秒）
progress_update_interval=500
```

### 网络连接配置

```ini
[network]
# 连接超时时间（秒）
connect_timeout=30

# 发送超时时间（秒）
send_timeout=10

# 接收超时时间（秒）
recv_timeout=10

# 心跳间隔（秒）
heartbeat_interval=30

# 重连间隔（秒）
reconnect_interval=5

# 最大重连次数
max_reconnect_attempts=3
```

### 日志配置详解

**现代化日志配置（基于spdlog）：**

#### 1. 程序化配置（推荐）
```cpp
// 或使用自定义配置
MidPlayerConfig::LoggingConfig config;
config.logLevel = "debug";
config.logFile = "logs/midplayer_{}.log";     // 支持日期占位符
config.consoleOutput = true;
config.fileOutput = true;
config.rotationType = "daily";                // daily/size/none
config.rotationHour = 0;                      // 每日轮转时间
config.rotationMinute = 0;
config.maxFileSize = 50 * 1024 * 1024;        // 50MB
config.maxFiles = 10;
config.singleThread = false;                  // 启用多线程
config.consolePattern = "%Y-%m-%d %H:%M:%S.%e [%t] [%^%l%$] %n - %v";
config.filePattern = "%Y-%m-%d %H:%M:%S.%e [%t] [%l] %n - %v";

logger.initialize(config);
```

#### 2. 配置文件支持
```ini
[logging]
log_level=info
log_file=logs/midplayer.log
console_output=true
file_output=true
rotation_type=daily
rotation_hour=0
rotation_minute=0
max_file_size=52428800
max_files=10
single_thread=false
console_pattern=%Y-%m-%d %H:%M:%S.%e [%t] [%^%l%$] %n - %v
file_pattern=%Y-%m-%d %H:%M:%S.%e [%t] [%l] %n - %v
```

## API接口说明

### 核心API接口

#### AtsMidParser 静态方法

```cpp
// 验证MID请求消息
static bool isValidMidRequest(uint64_t line, std::string& row);

// 提取请求和响应数据
static bool extractReqRes(uint64_t line, const std::string& row,
                         std::string& req, std::string& res);

// 从消息行提取完整信息
static bool extractReqResFromLine(uint64_t line, const std::string& row,
                                 std::string& req, std::string& res,
                                 std::string& stk, std::string& cls);

// 提取指定字段
static std::string extractFields(uint64_t line, const std::string& row, int field);
static bool extractFields(uint64_t line, const std::string& row,
                         int field, std::string& val);
static bool extractFields(const char* data, int codeField, int detailField,
                         std::string& codeValue, std::string& detailValue);
```

#### MidGateway 主要方法

```cpp
// 连接管理
bool Connect(const char* server, int port, int timeout = 30);
void Disconnect();
bool IsConnected() const;

// 消息发送
int SendRequest(const char* request, int length);
int SendRequestEx(const char* function, const char* request, int length);

// 消息接收
int ReceiveResponse(char* response, int maxLength, int timeout = 10);
int ReceiveResponseEx(char* response, int maxLength, int timeout = 10);

// 错误处理
int GetLastError() const;
const char* GetLastErrorMessage() const;
void SetLastError(int errorCode, const char* errorMessage);
```

## 监控和诊断

### 性能指标监控

MidPlayer提供丰富的性能监控指标：

```cpp
struct PerformanceMetrics {
    // 文件处理指标
    uint64_t total_lines_processed;      // 总处理行数
    uint64_t valid_messages_count;       // 有效消息数量
    uint64_t invalid_messages_count;     // 无效消息数量
    uint64_t bytes_processed;            // 处理字节数

    // 时间指标
    std::chrono::milliseconds total_time;        // 总处理时间
    std::chrono::milliseconds parse_time;        // 解析时间
    std::chrono::milliseconds network_time;      // 网络时间

    // 速度指标
    double lines_per_second;             // 行处理速度
    double messages_per_second;          // 消息处理速度
    double bytes_per_second;             // 字节处理速度

    // 网络指标
    uint64_t messages_sent;              // 发送消息数
    uint64_t messages_received;          // 接收消息数
    uint64_t network_errors;             // 网络错误数

    // 内存指标
    size_t peak_memory_usage;            // 峰值内存使用
    size_t current_memory_usage;         // 当前内存使用
};
```

### 实时监控界面

程序运行时显示实时监控信息：

```
================== MidPlayer 实时监控 ==================
文件信息: /path/to/data.mid (1.2GB)
处理进度: [████████████████████████████████] 100% (1,234,567/1,234,567)
处理速度: 125,000 行/秒 | 45.6 MB/秒
有效消息: 1,200,000 条 | 无效消息: 34,567 条
网络状态: 已连接 | 发送: 1,200,000 | 接收: 1,199,950
内存使用: 85.6 MB / 100.0 MB (85.6%)
运行时间: 00:02:15.678
预计剩余: 00:00:00.000
========================================================
```

## 开发和调试

### 开发环境设置

1. **Visual Studio配置**
   ```
   平台工具集: v143 (Visual Studio 2022)
   C++标准: C++17 (/std:c++17)
   字符集: Unicode
   运行时库: 多线程 DLL (/MD)
   ```

2. **预处理器定义**
   ```
   WIN32
   _WINDOWS
   NDEBUG (Release模式)
   _DEBUG (Debug模式)
   _CRT_SECURE_NO_WARNINGS
   ```

3. **链接库依赖**
   ```
   ws2_32.lib      # Windows Socket
   kernel32.lib    # Windows内核
   user32.lib      # Windows用户界面
   advapi32.lib    # Windows高级API
   ```

### 调试技巧

1. **启用详细日志**
   ```ini
   [main]
   debug_level=3
   ```

2. **内存泄漏检测**
   ```cpp
   #ifdef _DEBUG
   #define _CRTDBG_MAP_ALLOC
   #include <crtdbg.h>

   int main() {
       _CrtSetDbgFlag(_CRTDBG_ALLOC_MEM_DF | _CRTDBG_LEAK_CHECK_DF);
       // ... 程序代码
   }
   #endif
   ```

3. **性能分析**
   ```cpp
   // 使用高精度计时器
   auto start = std::chrono::high_resolution_clock::now();
   // ... 执行代码
   auto end = std::chrono::high_resolution_clock::now();
   auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
   ```

## 版本历史

- **v1.0.0**：初始版本，基本MID协议支持
- **v1.1.0**：增加内存优化算法选择
- **v1.2.0**：支持多线程处理
- **v1.3.0**：增强错误处理和日志系统
- **v1.4.0**：优化网络通信性能
- **v1.5.0**：增加实时监控和统计功能
- **v2.0.0**：🎉 **重大更新 - 日志系统全面重构**
  - 统一日志管理器：从双管理器架构整合为单一MidPlayerLogger
  - 多线程安全：完全的线程安全日志记录，支持高并发场景
  - 现代化接口：基于spdlog的高性能日志系统
  - 多样化宏支持：MIDLOG/LOG4CXX/MFLOG三套接口并存
  - 流式操作符兼容：完全兼容现有的`<<`操作符代码
  - 格式化支持：现代C++格式化语法，性能更优
  - 配置驱动：灵活的配置系统，支持运行时调整
  - 代码组织优化：清晰的职责分离和依赖关系

## 许可证

本项目采用企业内部许可证，仅供ArkReplay系统内部使用。

---

*MidPlayer是ArkReplay-Agent系统的重要组成部分，专注于提供高性能、可靠的MID协议流量回放能力，为金融交易系统的测试和验证提供强有力的支持。*
